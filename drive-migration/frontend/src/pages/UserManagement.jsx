import React, { useState, useEffect } from "react";
import ErrorDisplay from "../components/ErrorDisplay";
import { ToastContainer, useToast } from "../components/Toast";
import { apiPost, apiGet, formatError } from "../utils/apiUtils";
import "./UserManagement.css";

function UserManagement() {
  const [users, setUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [forceScan, setForceScan] = useState(false);
  const [loading, setLoading] = useState(false);
  const [scanLoading, setScanLoading] = useState(false);
  const [error, setError] = useState(null);
  const [scanSession, setScanSession] = useState(null);

  // Toast notifications
  const { toasts, removeToast, showError, showSuccess, showWarning } =
    useToast();

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async (forceRefresh = false) => {
    setLoading(true);
    setError(null);

    try {
      const url = forceRefresh
        ? `/api/scan/users?forceScan=true`
        : `/api/scan/users`;

      const result = await apiGet(url);
      setUsers(result.users || []);
      showSuccess(`Tải thành công ${result.users?.length || 0} người dùng`);
    } catch (error) {
      console.error("Error fetching users:", error);
      const errorInfo = formatError(error);
      setError(error);
      showError(`Lỗi tải danh sách người dùng: ${errorInfo.message}`, {
        showDetails: true,
        details: errorInfo.details,
        duration: 8000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUserToggle = (user) => {
    setSelectedUsers((prev) => {
      const isSelected = prev.some((u) => u.userId === user.userId);
      if (isSelected) {
        return prev.filter((u) => u.userId !== user.userId);
      } else {
        return [...prev, user];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers([...users]);
    }
  };

  const handleStartScan = async () => {
    if (selectedUsers.length === 0) {
      showWarning("Vui lòng chọn ít nhất một người dùng để scan");
      return;
    }

    setScanLoading(true);
    setError(null);

    try {
      // Tạo danh sách email từ users được chọn
      const userEmails = selectedUsers.map((user) => user.email);

      const result = await apiPost("/api/scan/start", {
        scope: "selected_users",
        userEmails: userEmails,
        forceScan: forceScan,
      });

      setScanSession(result);
      showSuccess(
        `Bắt đầu scan thành công cho ${selectedUsers.length} người dùng!`
      );

      // Poll for scan completion
      pollScanProgress(result.sessionId);
    } catch (error) {
      console.error("Error starting scan:", error);
      const errorInfo = formatError(error);
      setError(error);
      showError(`Lỗi bắt đầu scan: ${errorInfo.message}`, {
        showDetails: true,
        details: errorInfo.details,
        duration: 8000,
      });
    } finally {
      setScanLoading(false);
    }
  };

  const pollScanProgress = async (sessionId) => {
    const interval = setInterval(async () => {
      try {
        const status = await apiGet(`/api/scan/status/${sessionId}`);
        setScanSession(status);

        if (status.status === "completed") {
          clearInterval(interval);
          showSuccess(
            `Scan hoàn thành! Tìm thấy ${status.total_files || 0} file.`
          );
        } else if (status.status === "failed") {
          clearInterval(interval);
          const errorMsg = status.error_message || "Scan thất bại";
          setError(new Error(errorMsg));
          showError(`Scan thất bại: ${errorMsg}`, { duration: 8000 });
        }
      } catch (error) {
        console.error("Error polling scan progress:", error);
        clearInterval(interval);
        const errorInfo = formatError(error);
        setError(error);
        showError(`Lỗi kiểm tra tiến trình scan: ${errorInfo.message}`, {
          showDetails: true,
          details: errorInfo.details,
          duration: 8000,
        });
      }
    }, 2000);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Chưa có thông tin";
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getInitials = (fullName) => {
    if (!fullName) return "??";
    return fullName
      .split(" ")
      .map((name) => name.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>👥 User Management & Scan</h1>
        <p>Quản lý người dùng và thực hiện scan files từ Google Workspace</p>
      </header>

      <main className="app-main">
        {error && (
          <ErrorDisplay
            error={error}
            title="Lỗi trong quá trình xử lý"
            onDismiss={() => setError(null)}
            onRetry={() => {
              setError(null);
              fetchUsers(forceScan);
            }}
          />
        )}

        {/* Stats Overview */}
        {users.length > 0 && (
          <div className="stats-overview">
            <div className="stats-card primary">
              <div className="stat-number">{users.length}</div>
              <div className="stat-label">Tổng số người dùng</div>
            </div>
            <div className="stats-card success">
              <div className="stat-number">
                {users.filter((u) => !u.suspended).length}
              </div>
              <div className="stat-label">Đang hoạt động</div>
            </div>
            <div className="stats-card warning">
              <div className="stat-number">
                {users.filter((u) => u.suspended).length}
              </div>
              <div className="stat-label">Bị khóa</div>
            </div>
            <div className="stats-card">
              <div className="stat-number">{selectedUsers.length}</div>
              <div className="stat-label">Đã chọn</div>
            </div>
          </div>
        )}

        {/* Controls Section */}
        <div className="controls-section">
          <h2>Cài đặt & Điều khiển</h2>

          <div className="force-scan-option">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={forceScan}
                onChange={(e) => setForceScan(e.target.checked)}
                disabled={loading || scanLoading}
              />
              <span className="checkbox-custom"></span>
              <span>
                Force Scan - Quét lại danh sách người dùng từ Google Workspace
              </span>
            </label>
            <div className="force-scan-description">
              Khi bật tùy chọn này, hệ thống sẽ quét lại toàn bộ danh sách người
              dùng từ Google Workspace thay vì sử dụng dữ liệu đã cache. Điều
              này đảm bảo dữ liệu luôn được cập nhật mới nhất nhưng có thể mất
              nhiều thời gian hơn.
            </div>
          </div>

          <div className="action-buttons">
            <button
              onClick={() => fetchUsers(forceScan)}
              className="btn btn-secondary"
              disabled={loading || scanLoading}
            >
              {loading ? (
                <>
                  <span
                    className="spinner"
                    style={{
                      width: "16px",
                      height: "16px",
                      marginRight: "0.5rem",
                    }}
                  ></span>
                  Đang tải...
                </>
              ) : (
                "🔄 Tải danh sách người dùng"
              )}
            </button>

            <button
              onClick={handleStartScan}
              className="btn btn-primary btn-large"
              disabled={selectedUsers.length === 0 || scanLoading}
            >
              {scanLoading ? (
                <>
                  <span
                    className="spinner"
                    style={{
                      width: "16px",
                      height: "16px",
                      marginRight: "0.5rem",
                    }}
                  ></span>
                  Đang bắt đầu scan...
                </>
              ) : (
                `🚀 Scan files (${selectedUsers.length} người dùng)`
              )}
            </button>
          </div>
        </div>

        {/* Scan Progress */}
        {scanSession && (
          <div className="scan-progress-mini">
            <h4>📊 Tiến trình Scan</h4>

            {scanSession.status === "running" && (
              <div className="progress-bar-container">
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{
                      width: `${Math.min(
                        100,
                        ((scanSession.processed_files || 0) /
                          Math.max(1, scanSession.total_files || 1)) *
                          100
                      )}%`,
                    }}
                  ></div>
                </div>
                <div className="progress-text">
                  {scanSession.processed_files || 0} /{" "}
                  {scanSession.total_files || 0} files
                </div>
              </div>
            )}

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
                gap: "1rem",
                marginTop: "1rem",
              }}
            >
              <div className="detail-row">
                <span className="detail-label">Trạng thái:</span>
                <span className={`detail-value status-${scanSession.status}`}>
                  {scanSession.status === "running" && "🔄 Đang chạy"}
                  {scanSession.status === "completed" && "✅ Hoàn thành"}
                  {scanSession.status === "failed" && "❌ Thất bại"}
                  {scanSession.status === "cancelled" && "⏹️ Đã hủy"}
                </span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Files:</span>
                <span className="detail-value">
                  {scanSession.total_files || 0}
                </span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Folders:</span>
                <span className="detail-value">
                  {scanSession.total_folders || 0}
                </span>
              </div>
              {scanSession.status === "completed" && (
                <div className="detail-row">
                  <span className="detail-label">Kích thước:</span>
                  <span className="detail-value">
                    {formatFileSize(scanSession.total_size || 0)}
                  </span>
                </div>
              )}
            </div>

            {scanSession.error_message && (
              <div
                style={{
                  marginTop: "1rem",
                  padding: "0.75rem",
                  background: "#fee2e2",
                  borderRadius: "6px",
                  borderLeft: "3px solid #dc2626",
                }}
              >
                <strong style={{ color: "#991b1b" }}>Lỗi:</strong>
                <span style={{ color: "#7f1d1d", marginLeft: "0.5rem" }}>
                  {scanSession.error_message}
                </span>
              </div>
            )}

            {scanSession.status === "completed" && (
              <div className="scan-success">
                <h4>🎉 Scan hoàn thành thành công!</h4>
                <p>
                  Đã quét thành công {scanSession.total_files || 0} files từ{" "}
                  {selectedUsers.length} người dùng.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Users List */}
        <div className="card">
          <div className="selection-summary">
            <div className="selection-info">
              <span>Tổng số người dùng: {users.length}</span>
              <span>Đã chọn: {selectedUsers.length}</span>
            </div>

            <label className="select-all-checkbox">
              <input
                type="checkbox"
                checked={
                  selectedUsers.length === users.length && users.length > 0
                }
                onChange={handleSelectAll}
                disabled={loading || scanLoading}
              />
              <span className="checkbox-custom"></span>
              <span>Chọn tất cả</span>
            </label>
          </div>

          {loading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Đang tải danh sách người dùng...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="empty-state">
              <p>
                Không có người dùng nào. Vui lòng thử tải lại với Force Scan.
              </p>
            </div>
          ) : (
            <div className="users-table">
              <div className="table-header">
                <div>Chọn</div>
                <div>Người dùng</div>
                <div>Email</div>
                <div>Trạng thái</div>
                <div>Đăng nhập cuối</div>
                <div>Ngày tạo</div>
              </div>

              <div className="table-body">
                {users.map((user) => (
                  <div
                    key={user.userId}
                    className={`table-row ${
                      selectedUsers.some((u) => u.userId === user.userId)
                        ? "selected"
                        : ""
                    }`}
                  >
                    <div className="col-select">
                      <label className="file-checkbox">
                        <input
                          type="checkbox"
                          checked={selectedUsers.some(
                            (u) => u.userId === user.userId
                          )}
                          onChange={() => handleUserToggle(user)}
                          disabled={loading || scanLoading}
                        />
                        <span className="checkbox-custom"></span>
                      </label>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "0.75rem",
                      }}
                    >
                      <div
                        className="user-avatar"
                        style={{
                          width: "40px",
                          height: "40px",
                          borderRadius: "50%",
                          background: user.suspended
                            ? "linear-gradient(135deg, #ef4444, #dc2626)"
                            : "linear-gradient(135deg, #3b82f6, #1d4ed8)",
                          color: "white",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          fontSize: "0.875rem",
                          fontWeight: "600",
                          flexShrink: 0,
                        }}
                      >
                        {getInitials(user.fullName)}
                      </div>
                      <div>
                        <div className="file-name">
                          {user.fullName || "Chưa có tên"}
                        </div>
                        <div className="file-path">
                          {user.givenName} {user.familyName}
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="file-name">{user.email}</div>
                      <div className="file-path">ID: {user.userId}</div>
                    </div>
                    <div>
                      <span
                        className={`user-status ${
                          user.suspended ? "suspended" : "active"
                        }`}
                        style={{
                          padding: "0.25rem 0.75rem",
                          borderRadius: "20px",
                          fontSize: "0.75rem",
                          fontWeight: "500",
                          background: user.suspended ? "#fee2e2" : "#dcfce7",
                          color: user.suspended ? "#991b1b" : "#166534",
                        }}
                      >
                        {user.suspended ? "🔒 Bị khóa" : "✅ Hoạt động"}
                      </span>
                    </div>
                    <div>
                      <div style={{ fontSize: "0.875rem" }}>
                        {formatDate(user.lastLoginTime)}
                      </div>
                    </div>
                    <div>
                      <div style={{ fontSize: "0.875rem" }}>
                        {formatDate(user.createdAt)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
    </div>
  );
}

export default UserManagement;
