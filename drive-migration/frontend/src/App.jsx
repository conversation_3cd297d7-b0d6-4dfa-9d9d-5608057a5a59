import React, { useState } from 'react';
import ScopeSelector from './components/ScopeSelector';
import FileList from './components/FileList';
import ScanProgress from './components/ScanProgress';
import ErrorDisplay from './components/ErrorDisplay';
import ErrorTestPanel from './components/ErrorTestPanel';
import { ToastContainer, useToast } from './components/Toast';
import { apiPost, apiGet, formatError } from './utils/apiUtils';
import './App.css';

function App() {
    const [currentStep, setCurrentStep] = useState('scope'); // scope, scanning, files, migration
    const [scanSession, setScanSession] = useState(null);
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [treeData, setTreeData] = useState([]); // Store tree data for offline processing
    const [userEmail, setUserEmail] = useState('<EMAIL>');
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(false);

    // Toast notifications
    const { toasts, removeToast, showError, showSuccess, showWarning } = useToast();

    const handleScopeSelected = async (scope, options) => {
        console.log('Scope selected:', scope, options);
        setError(null);
        setLoading(true);
        setCurrentStep('scanning');

        try {
            // Start scanning
            const result = await apiPost('/api/scan/start', {
                userEmail,
                scope,
                ...options
            });

            setScanSession(result);
            showSuccess('Bắt đầu quét thành công!');

            // Poll for scan completion
            pollScanProgress(result.sessionId);

        } catch (error) {
            console.error('Error starting scan:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi bắt đầu quét: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000
            });
            setCurrentStep('scope');
        } finally {
            setLoading(false);
        }
    };

    const pollScanProgress = async (sessionId) => {
        const interval = setInterval(async () => {
            try {
                const status = await apiGet(`/api/scan/status/${sessionId}`);

                setScanSession(status);

                if (status.status === 'completed') {
                    clearInterval(interval);
                    showSuccess(`Quét hoàn thành! Tìm thấy ${status.total_files || 0} file.`);
                    setCurrentStep('files');
                } else if (status.status === 'failed') {
                    clearInterval(interval);
                    const errorMsg = status.error_message || 'Quét thất bại';
                    setError(new Error(errorMsg));
                    showError(`Quét thất bại: ${errorMsg}`, { duration: 8000 });
                    setCurrentStep('scope');
                }
            } catch (error) {
                console.error('Error polling scan progress:', error);
                clearInterval(interval);
                const errorInfo = formatError(error);
                setError(error);
                showError(`Lỗi kiểm tra tiến trình quét: ${errorInfo.message}`, {
                    showDetails: true,
                    details: errorInfo.details,
                    duration: 8000
                });
                setCurrentStep('scope');
            }
        }, 2000);
    };

    const handleFilesSelected = (files) => {
        setSelectedFiles(files);
        // Không tự động chuyển step, chỉ cập nhật selection
    };

    const handleProceedToMigration = async () => {
        if (selectedFiles.length === 0) {
            showWarning('Vui lòng chọn ít nhất một file để migration.');
            return;
        }

        setLoading(true);
        setError(null);

        try {
            // Debug: log selected files structure
            console.log('Selected files:', selectedFiles);
            console.log('Tree data for offline processing:', treeData);
            
            // Check if all files are selected (Select All case)
            const allFiles = getAllFiles(treeData);
            const isAllFilesSelected = selectedFiles.length === allFiles.length && allFiles.length > 0;
            
            if (isAllFilesSelected) {
                // Call API with isSelectAll parameter for Select All case
                console.log('All files selected - calling API with isSelectAll=true');
                await apiPost('/api/scan/files/select', {
                    sessionId: scanSession.id,
                    isSelectAll: true,
                    isSelected: true
                });
                showSuccess('Đã đánh dấu tất cả files được chọn trong cơ sở dữ liệu.');
            } else {
                // Partial selection - call API with specific IDs
                const fileIds = selectedFiles.map(file => file.id);
                
                // Get folder IDs that have 'all' selection state (offline processing)
                const folderIds = getFullySelectedFolderIds();
                
                // Combine file and folder IDs
                const allIds = [...fileIds, ...folderIds];
                
                console.log('File IDs to update:', fileIds);
                console.log('Folder IDs to update (All Selected):', folderIds);
                console.log('Combined IDs to update:', allIds);
                
                // Call API to update selection status in database
                await apiPost('/api/scan/files/select', {
                    sessionId: scanSession.id,
                    ids: allIds,
                    isSelected: true
                });

                showSuccess(`Đã đánh dấu ${selectedFiles.length} file và ${folderIds.length} folder được chọn trong cơ sở dữ liệu.`);
            }
            
            setCurrentStep('migration');

        } catch (error) {
            console.error('Error updating file selection:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi cập nhật trạng thái chọn file: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000
            });
        } finally {
            setLoading(false);
        }
    };

    const handleStartMigration = async () => {
        setError(null);
        setLoading(true);

        try {
            const result = await apiPost('/api/migration/start', {
                userEmail,
                scanSessionId: scanSession.id,
            });

            console.log('Migration started:', result);
            showSuccess(`Bắt đầu migration ${selectedFiles.length} file thành công!`);

        } catch (error) {
            console.error('Error starting migration:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi bắt đầu migration: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000
            });
        } finally {
            setLoading(false);
        }
    };

    // Handle select all toggle for UI state only (no API call)
    const handleSelectAllToggle = async (isSelectingAll) => {
        // Just for logging - no API call here
        console.log('Select All toggled:', isSelectingAll);
        
        if (isSelectingAll) {
            showSuccess('Đã chọn tất cả files. Nhấn "Continue" để cập nhật cơ sở dữ liệu.');
        } else {
            showSuccess('Đã bỏ chọn tất cả files.');
        }
    };

    // Helper function to get all files within a folder (recursively)
    const getAllFilesInFolder = (folderNode) => {
        let files = [];
        if (folderNode.children) {
            folderNode.children.forEach(child => {
                if (child.type === 'file') {
                    files.push(child);
                } else if (child.type === 'folder') {
                    files = files.concat(getAllFilesInFolder(child));
                }
            });
        }
        return files;
    };

    // Helper function to get folder selection state offline
    const getFolderSelectionState = (folderNode) => {
        const allFilesInFolder = getAllFilesInFolder(folderNode);
        if (allFilesInFolder.length === 0) return 'none';
        
        const selectedFilesInFolder = allFilesInFolder.filter(file => 
            selectedFiles.some(sf => sf.id === file.id)
        );
        
        if (selectedFilesInFolder.length === 0) return 'none';
        if (selectedFilesInFolder.length === allFilesInFolder.length) return 'all';
        return 'some';
    };

    // Helper function to get folder IDs with 'all' selection state (offline)
    const getFullySelectedFolderIds = () => {
        const fullySelectedFolders = [];
        
        // Recursive function to check folder selection state
        const checkFolderSelection = (nodes) => {
            nodes.forEach(node => {
                if (node.type === 'folder') {
                    // Get selection state for this folder
                    const selectionState = getFolderSelectionState(node);
                    
                    if (selectionState === 'all') {
                        fullySelectedFolders.push(node.id);
                    }
                    
                    // Check children folders recursively
                    if (node.children) {
                        checkFolderSelection(node.children);
                    }
                }
            });
        };
        
        checkFolderSelection(treeData);
        return fullySelectedFolders;
    };

    // Helper function to get all files from tree data
    const getAllFiles = (nodes) => {
        let files = [];
        nodes.forEach(node => {
            if (node.type === 'file') {
                files.push(node);
            }
            if (node.children) {
                files = files.concat(getAllFiles(node.children));
            }
        });
        return files;
    };

    return (
        <div className="app">
            <header className="app-header">
                <h1>🚀 Drive-to-Lark Migrator</h1>
                <div className="user-info">
                    <input
                        type="email"
                        placeholder="Enter your email"
                        value={userEmail}
                        onChange={(e) => setUserEmail(e.target.value)}
                        className="user-email-input"
                    />
                </div>
            </header>

            <main className="app-main">
                <div className="step-indicator">
                    <div className={`step ${currentStep === 'scope' ? 'active' : currentStep !== 'scope' ? 'completed' : ''}`}>
                        1. Select Scope
                    </div>
                    <div className={`step ${currentStep === 'scanning' ? 'active' : ''}`}>
                        2. Scanning
                    </div>
                    <div className={`step ${currentStep === 'files' ? 'active' : ''}`}>
                        3. Select Files
                    </div>
                    <div className={`step ${currentStep === 'migration' ? 'active' : ''}`}>
                        4. Migration
                    </div>
                </div>

                {/* Development Error Testing */}
                {import.meta.env.DEV && (
                    <ErrorTestPanel
                        onError={(error, testName) => {
                            const errorInfo = formatError(error);
                            setError(error);
                            showError(`${testName}: ${errorInfo.message}`, {
                                showDetails: true,
                                details: errorInfo.details,
                                duration: 10000
                            });
                        }}
                        onSuccess={(message) => {
                            showSuccess(message);
                        }}
                    />
                )}

                {error && (
                    <ErrorDisplay
                        error={error}
                        title="Lỗi trong quá trình xử lý"
                        onDismiss={() => setError(null)}
                        onRetry={() => {
                            setError(null);
                            // Retry based on current step
                            if (currentStep === 'scope') {
                                // User can retry by clicking scan again
                            }
                        }}
                    />
                )}

                <div className="step-content">
                    {currentStep === 'scope' && (
                        <ScopeSelector
                            onScopeSelected={handleScopeSelected}
                            userEmail={userEmail}
                            loading={loading}
                        />
                    )}

                    {currentStep === 'scanning' && (
                        <ScanProgress
                            scanSession={scanSession}
                            onCancel={() => setCurrentStep('scope')}
                        />
                    )}

                    {currentStep === 'files' && (
                        <FileList
                            scanSession={scanSession}
                            onFilesSelected={handleFilesSelected}
                            selectedFiles={selectedFiles}
                            onProceedToMigration={handleProceedToMigration}
                            onTreeDataLoaded={setTreeData}
                            onSelectAllToggle={handleSelectAllToggle}
                        />
                    )}

                    {currentStep === 'migration' && (
                        <div className="migration-step">
                            <h2>Ready to Migrate</h2>
                            <p>Selected {selectedFiles.length} files for migration</p>
                            <button
                                onClick={handleStartMigration}
                                className="btn btn-primary"
                                disabled={selectedFiles.length === 0}
                            >
                                Start Migration
                            </button>
                        </div>
                    )}
                </div>
            </main>

            {/* Toast Notifications */}
            <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
        </div>
    );
}

export default App;
