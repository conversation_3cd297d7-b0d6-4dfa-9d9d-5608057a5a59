(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const a of l)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const a={};return l.integrity&&(a.integrity=l.integrity),l.referrerPolicy&&(a.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?a.credentials="include":l.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(l){if(l.ep)return;l.ep=!0;const a=n(l);fetch(l.href,a)}})();function md(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Oo={exports:{}},Ll={},Uo={exports:{}},U={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gr=Symbol.for("react.element"),vd=Symbol.for("react.portal"),gd=Symbol.for("react.fragment"),xd=Symbol.for("react.strict_mode"),yd=Symbol.for("react.profiler"),jd=Symbol.for("react.provider"),wd=Symbol.for("react.context"),Nd=Symbol.for("react.forward_ref"),Sd=Symbol.for("react.suspense"),kd=Symbol.for("react.memo"),Cd=Symbol.for("react.lazy"),wi=Symbol.iterator;function Ed(e){return e===null||typeof e!="object"?null:(e=wi&&e[wi]||e["@@iterator"],typeof e=="function"?e:null)}var Bo={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ao=Object.assign,Ho={};function Cn(e,t,n){this.props=e,this.context=t,this.refs=Ho,this.updater=n||Bo}Cn.prototype.isReactComponent={};Cn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Cn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Wo(){}Wo.prototype=Cn.prototype;function xa(e,t,n){this.props=e,this.context=t,this.refs=Ho,this.updater=n||Bo}var ya=xa.prototype=new Wo;ya.constructor=xa;Ao(ya,Cn.prototype);ya.isPureReactComponent=!0;var Ni=Array.isArray,Vo=Object.prototype.hasOwnProperty,ja={current:null},bo={key:!0,ref:!0,__self:!0,__source:!0};function Qo(e,t,n){var r,l={},a=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(a=""+t.key),t)Vo.call(t,r)&&!bo.hasOwnProperty(r)&&(l[r]=t[r]);var o=arguments.length-2;if(o===1)l.children=n;else if(1<o){for(var u=Array(o),c=0;c<o;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)l[r]===void 0&&(l[r]=o[r]);return{$$typeof:gr,type:e,key:a,ref:i,props:l,_owner:ja.current}}function _d(e,t){return{$$typeof:gr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function wa(e){return typeof e=="object"&&e!==null&&e.$$typeof===gr}function Pd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Si=/\/+/g;function Gl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Pd(""+e.key):t.toString(36)}function Vr(e,t,n,r,l){var a=typeof e;(a==="undefined"||a==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(a){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case gr:case vd:i=!0}}if(i)return i=e,l=l(i),e=r===""?"."+Gl(i,0):r,Ni(l)?(n="",e!=null&&(n=e.replace(Si,"$&/")+"/"),Vr(l,t,n,"",function(c){return c})):l!=null&&(wa(l)&&(l=_d(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(Si,"$&/")+"/")+e)),t.push(l)),1;if(i=0,r=r===""?".":r+":",Ni(e))for(var o=0;o<e.length;o++){a=e[o];var u=r+Gl(a,o);i+=Vr(a,t,n,u,l)}else if(u=Ed(e),typeof u=="function")for(e=u.call(e),o=0;!(a=e.next()).done;)a=a.value,u=r+Gl(a,o++),i+=Vr(a,t,n,u,l);else if(a==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Er(e,t,n){if(e==null)return e;var r=[],l=0;return Vr(e,r,"","",function(a){return t.call(n,a,l++)}),r}function Ld(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ye={current:null},br={transition:null},Fd={ReactCurrentDispatcher:ye,ReactCurrentBatchConfig:br,ReactCurrentOwner:ja};function Ko(){throw Error("act(...) is not supported in production builds of React.")}U.Children={map:Er,forEach:function(e,t,n){Er(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Er(e,function(){t++}),t},toArray:function(e){return Er(e,function(t){return t})||[]},only:function(e){if(!wa(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};U.Component=Cn;U.Fragment=gd;U.Profiler=yd;U.PureComponent=xa;U.StrictMode=xd;U.Suspense=Sd;U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Fd;U.act=Ko;U.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ao({},e.props),l=e.key,a=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(a=t.ref,i=ja.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(u in t)Vo.call(t,u)&&!bo.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&o!==void 0?o[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){o=Array(u);for(var c=0;c<u;c++)o[c]=arguments[c+2];r.children=o}return{$$typeof:gr,type:e.type,key:l,ref:a,props:r,_owner:i}};U.createContext=function(e){return e={$$typeof:wd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:jd,_context:e},e.Consumer=e};U.createElement=Qo;U.createFactory=function(e){var t=Qo.bind(null,e);return t.type=e,t};U.createRef=function(){return{current:null}};U.forwardRef=function(e){return{$$typeof:Nd,render:e}};U.isValidElement=wa;U.lazy=function(e){return{$$typeof:Cd,_payload:{_status:-1,_result:e},_init:Ld}};U.memo=function(e,t){return{$$typeof:kd,type:e,compare:t===void 0?null:t}};U.startTransition=function(e){var t=br.transition;br.transition={};try{e()}finally{br.transition=t}};U.unstable_act=Ko;U.useCallback=function(e,t){return ye.current.useCallback(e,t)};U.useContext=function(e){return ye.current.useContext(e)};U.useDebugValue=function(){};U.useDeferredValue=function(e){return ye.current.useDeferredValue(e)};U.useEffect=function(e,t){return ye.current.useEffect(e,t)};U.useId=function(){return ye.current.useId()};U.useImperativeHandle=function(e,t,n){return ye.current.useImperativeHandle(e,t,n)};U.useInsertionEffect=function(e,t){return ye.current.useInsertionEffect(e,t)};U.useLayoutEffect=function(e,t){return ye.current.useLayoutEffect(e,t)};U.useMemo=function(e,t){return ye.current.useMemo(e,t)};U.useReducer=function(e,t,n){return ye.current.useReducer(e,t,n)};U.useRef=function(e){return ye.current.useRef(e)};U.useState=function(e){return ye.current.useState(e)};U.useSyncExternalStore=function(e,t,n){return ye.current.useSyncExternalStore(e,t,n)};U.useTransition=function(){return ye.current.useTransition()};U.version="18.3.1";Uo.exports=U;var g=Uo.exports;const Rd=md(g);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Td=g,Dd=Symbol.for("react.element"),zd=Symbol.for("react.fragment"),Md=Object.prototype.hasOwnProperty,$d=Td.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Id={key:!0,ref:!0,__self:!0,__source:!0};function Go(e,t,n){var r,l={},a=null,i=null;n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Md.call(t,r)&&!Id.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:Dd,type:e,key:a,ref:i,props:l,_owner:$d.current}}Ll.Fragment=zd;Ll.jsx=Go;Ll.jsxs=Go;Oo.exports=Ll;var s=Oo.exports,Ns={},Yo={exports:{}},Fe={},Xo={exports:{}},Jo={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,$){var O=R.length;R.push($);e:for(;0<O;){var Y=O-1>>>1,ne=R[Y];if(0<l(ne,$))R[Y]=$,R[O]=ne,O=Y;else break e}}function n(R){return R.length===0?null:R[0]}function r(R){if(R.length===0)return null;var $=R[0],O=R.pop();if(O!==$){R[0]=O;e:for(var Y=0,ne=R.length,D=ne>>>1;Y<D;){var I=2*(Y+1)-1,X=R[I],oe=I+1,Cr=R[oe];if(0>l(X,O))oe<ne&&0>l(Cr,X)?(R[Y]=Cr,R[oe]=O,Y=oe):(R[Y]=X,R[I]=O,Y=I);else if(oe<ne&&0>l(Cr,O))R[Y]=Cr,R[oe]=O,Y=oe;else break e}}return $}function l(R,$){var O=R.sortIndex-$.sortIndex;return O!==0?O:R.id-$.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;e.unstable_now=function(){return a.now()}}else{var i=Date,o=i.now();e.unstable_now=function(){return i.now()-o}}var u=[],c=[],f=1,v=null,h=3,S=!1,N=!1,_=!1,L=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(R){for(var $=n(c);$!==null;){if($.callback===null)r(c);else if($.startTime<=R)r(c),$.sortIndex=$.expirationTime,t(u,$);else break;$=n(c)}}function w(R){if(_=!1,m(R),!N)if(n(u)!==null)N=!0,pt(x);else{var $=n(c);$!==null&&Q(w,$.startTime-R)}}function x(R,$){N=!1,_&&(_=!1,p(j),j=-1),S=!0;var O=h;try{for(m($),v=n(u);v!==null&&(!(v.expirationTime>$)||R&&!k());){var Y=v.callback;if(typeof Y=="function"){v.callback=null,h=v.priorityLevel;var ne=Y(v.expirationTime<=$);$=e.unstable_now(),typeof ne=="function"?v.callback=ne:v===n(u)&&r(u),m($)}else r(u);v=n(u)}if(v!==null)var D=!0;else{var I=n(c);I!==null&&Q(w,I.startTime-$),D=!1}return D}finally{v=null,h=O,S=!1}}var E=!1,P=null,j=-1,C=5,y=-1;function k(){return!(e.unstable_now()-y<C)}function T(){if(P!==null){var R=e.unstable_now();y=R;var $=!0;try{$=P(!0,R)}finally{$?z():(E=!1,P=null)}}else E=!1}var z;if(typeof d=="function")z=function(){d(T)};else if(typeof MessageChannel<"u"){var B=new MessageChannel,Mt=B.port2;B.port1.onmessage=T,z=function(){Mt.postMessage(null)}}else z=function(){L(T,0)};function pt(R){P=R,E||(E=!0,z())}function Q(R,$){j=L(function(){R(e.unstable_now())},$)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_continueExecution=function(){N||S||(N=!0,pt(x))},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(R){switch(h){case 1:case 2:case 3:var $=3;break;default:$=h}var O=h;h=$;try{return R()}finally{h=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(R,$){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var O=h;h=R;try{return $()}finally{h=O}},e.unstable_scheduleCallback=function(R,$,O){var Y=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?Y+O:Y):O=Y,R){case 1:var ne=-1;break;case 2:ne=250;break;case 5:ne=**********;break;case 4:ne=1e4;break;default:ne=5e3}return ne=O+ne,R={id:f++,callback:$,priorityLevel:R,startTime:O,expirationTime:ne,sortIndex:-1},O>Y?(R.sortIndex=O,t(c,R),n(u)===null&&R===n(c)&&(_?(p(j),j=-1):_=!0,Q(w,O-Y))):(R.sortIndex=ne,t(u,R),N||S||(N=!0,pt(x))),R},e.unstable_shouldYield=k,e.unstable_wrapCallback=function(R){var $=h;return function(){var O=h;h=$;try{return R.apply(this,arguments)}finally{h=O}}}})(Jo);Xo.exports=Jo;var Od=Xo.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ud=g,Le=Od;function F(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Zo=new Set,qn={};function Xt(e,t){xn(e,t),xn(e+"Capture",t)}function xn(e,t){for(qn[e]=t,e=0;e<t.length;e++)Zo.add(t[e])}var it=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ss=Object.prototype.hasOwnProperty,Bd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ki={},Ci={};function Ad(e){return Ss.call(Ci,e)?!0:Ss.call(ki,e)?!1:Bd.test(e)?Ci[e]=!0:(ki[e]=!0,!1)}function Hd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Wd(e,t,n,r){if(t===null||typeof t>"u"||Hd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function je(e,t,n,r,l,a,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var de={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){de[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];de[t]=new je(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){de[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){de[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){de[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){de[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){de[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){de[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){de[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var Na=/[\-:]([a-z])/g;function Sa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Na,Sa);de[t]=new je(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Na,Sa);de[t]=new je(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Na,Sa);de[t]=new je(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){de[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});de.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){de[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function ka(e,t,n,r){var l=de.hasOwnProperty(t)?de[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Wd(t,n,l,r)&&(n=null),r||l===null?Ad(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ft=Ud.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_r=Symbol.for("react.element"),qt=Symbol.for("react.portal"),en=Symbol.for("react.fragment"),Ca=Symbol.for("react.strict_mode"),ks=Symbol.for("react.profiler"),qo=Symbol.for("react.provider"),eu=Symbol.for("react.context"),Ea=Symbol.for("react.forward_ref"),Cs=Symbol.for("react.suspense"),Es=Symbol.for("react.suspense_list"),_a=Symbol.for("react.memo"),vt=Symbol.for("react.lazy"),tu=Symbol.for("react.offscreen"),Ei=Symbol.iterator;function Rn(e){return e===null||typeof e!="object"?null:(e=Ei&&e[Ei]||e["@@iterator"],typeof e=="function"?e:null)}var q=Object.assign,Yl;function Bn(e){if(Yl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Yl=t&&t[1]||""}return`
`+Yl+e}var Xl=!1;function Jl(e,t){if(!e||Xl)return"";Xl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),a=r.stack.split(`
`),i=l.length-1,o=a.length-1;1<=i&&0<=o&&l[i]!==a[o];)o--;for(;1<=i&&0<=o;i--,o--)if(l[i]!==a[o]){if(i!==1||o!==1)do if(i--,o--,0>o||l[i]!==a[o]){var u=`
`+l[i].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=i&&0<=o);break}}}finally{Xl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Bn(e):""}function Vd(e){switch(e.tag){case 5:return Bn(e.type);case 16:return Bn("Lazy");case 13:return Bn("Suspense");case 19:return Bn("SuspenseList");case 0:case 2:case 15:return e=Jl(e.type,!1),e;case 11:return e=Jl(e.type.render,!1),e;case 1:return e=Jl(e.type,!0),e;default:return""}}function _s(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case en:return"Fragment";case qt:return"Portal";case ks:return"Profiler";case Ca:return"StrictMode";case Cs:return"Suspense";case Es:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case eu:return(e.displayName||"Context")+".Consumer";case qo:return(e._context.displayName||"Context")+".Provider";case Ea:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case _a:return t=e.displayName||null,t!==null?t:_s(e.type)||"Memo";case vt:t=e._payload,e=e._init;try{return _s(e(t))}catch{}}return null}function bd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return _s(t);case 8:return t===Ca?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ft(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function nu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Qd(e){var t=nu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){r=""+i,a.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Pr(e){e._valueTracker||(e._valueTracker=Qd(e))}function ru(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=nu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ll(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ps(e,t){var n=t.checked;return q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function _i(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ft(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function lu(e,t){t=t.checked,t!=null&&ka(e,"checked",t,!1)}function Ls(e,t){lu(e,t);var n=Ft(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Fs(e,t.type,n):t.hasOwnProperty("defaultValue")&&Fs(e,t.type,Ft(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Pi(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Fs(e,t,n){(t!=="number"||ll(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var An=Array.isArray;function fn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ft(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Rs(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(F(91));return q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Li(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(F(92));if(An(n)){if(1<n.length)throw Error(F(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ft(n)}}function su(e,t){var n=Ft(t.value),r=Ft(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Fi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function au(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ts(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?au(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Lr,iu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Lr=Lr||document.createElement("div"),Lr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Lr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function er(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Vn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Kd=["Webkit","ms","Moz","O"];Object.keys(Vn).forEach(function(e){Kd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Vn[t]=Vn[e]})});function ou(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Vn.hasOwnProperty(e)&&Vn[e]?(""+t).trim():t+"px"}function uu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=ou(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Gd=q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ds(e,t){if(t){if(Gd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(F(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(F(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(F(61))}if(t.style!=null&&typeof t.style!="object")throw Error(F(62))}}function zs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ms=null;function Pa(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var $s=null,hn=null,pn=null;function Ri(e){if(e=jr(e)){if(typeof $s!="function")throw Error(F(280));var t=e.stateNode;t&&(t=zl(t),$s(e.stateNode,e.type,t))}}function cu(e){hn?pn?pn.push(e):pn=[e]:hn=e}function du(){if(hn){var e=hn,t=pn;if(pn=hn=null,Ri(e),t)for(e=0;e<t.length;e++)Ri(t[e])}}function fu(e,t){return e(t)}function hu(){}var Zl=!1;function pu(e,t,n){if(Zl)return e(t,n);Zl=!0;try{return fu(e,t,n)}finally{Zl=!1,(hn!==null||pn!==null)&&(hu(),du())}}function tr(e,t){var n=e.stateNode;if(n===null)return null;var r=zl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(F(231,t,typeof n));return n}var Is=!1;if(it)try{var Tn={};Object.defineProperty(Tn,"passive",{get:function(){Is=!0}}),window.addEventListener("test",Tn,Tn),window.removeEventListener("test",Tn,Tn)}catch{Is=!1}function Yd(e,t,n,r,l,a,i,o,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var bn=!1,sl=null,al=!1,Os=null,Xd={onError:function(e){bn=!0,sl=e}};function Jd(e,t,n,r,l,a,i,o,u){bn=!1,sl=null,Yd.apply(Xd,arguments)}function Zd(e,t,n,r,l,a,i,o,u){if(Jd.apply(this,arguments),bn){if(bn){var c=sl;bn=!1,sl=null}else throw Error(F(198));al||(al=!0,Os=c)}}function Jt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function mu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ti(e){if(Jt(e)!==e)throw Error(F(188))}function qd(e){var t=e.alternate;if(!t){if(t=Jt(e),t===null)throw Error(F(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var a=l.alternate;if(a===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===a.child){for(a=l.child;a;){if(a===n)return Ti(l),e;if(a===r)return Ti(l),t;a=a.sibling}throw Error(F(188))}if(n.return!==r.return)n=l,r=a;else{for(var i=!1,o=l.child;o;){if(o===n){i=!0,n=l,r=a;break}if(o===r){i=!0,r=l,n=a;break}o=o.sibling}if(!i){for(o=a.child;o;){if(o===n){i=!0,n=a,r=l;break}if(o===r){i=!0,r=a,n=l;break}o=o.sibling}if(!i)throw Error(F(189))}}if(n.alternate!==r)throw Error(F(190))}if(n.tag!==3)throw Error(F(188));return n.stateNode.current===n?e:t}function vu(e){return e=qd(e),e!==null?gu(e):null}function gu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=gu(e);if(t!==null)return t;e=e.sibling}return null}var xu=Le.unstable_scheduleCallback,Di=Le.unstable_cancelCallback,ef=Le.unstable_shouldYield,tf=Le.unstable_requestPaint,te=Le.unstable_now,nf=Le.unstable_getCurrentPriorityLevel,La=Le.unstable_ImmediatePriority,yu=Le.unstable_UserBlockingPriority,il=Le.unstable_NormalPriority,rf=Le.unstable_LowPriority,ju=Le.unstable_IdlePriority,Fl=null,Ze=null;function lf(e){if(Ze&&typeof Ze.onCommitFiberRoot=="function")try{Ze.onCommitFiberRoot(Fl,e,void 0,(e.current.flags&128)===128)}catch{}}var Ve=Math.clz32?Math.clz32:of,sf=Math.log,af=Math.LN2;function of(e){return e>>>=0,e===0?32:31-(sf(e)/af|0)|0}var Fr=64,Rr=4194304;function Hn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ol(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,a=e.pingedLanes,i=n&268435455;if(i!==0){var o=i&~l;o!==0?r=Hn(o):(a&=i,a!==0&&(r=Hn(a)))}else i=n&~l,i!==0?r=Hn(i):a!==0&&(r=Hn(a));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,a=t&-t,l>=a||l===16&&(a&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ve(t),l=1<<n,r|=e[n],t&=~l;return r}function uf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function cf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-Ve(a),o=1<<i,u=l[i];u===-1?(!(o&n)||o&r)&&(l[i]=uf(o,t)):u<=t&&(e.expiredLanes|=o),a&=~o}}function Us(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function wu(){var e=Fr;return Fr<<=1,!(Fr&4194240)&&(Fr=64),e}function ql(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function xr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ve(t),e[t]=n}function df(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Ve(n),a=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~a}}function Fa(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ve(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var H=0;function Nu(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Su,Ra,ku,Cu,Eu,Bs=!1,Tr=[],Nt=null,St=null,kt=null,nr=new Map,rr=new Map,xt=[],ff="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zi(e,t){switch(e){case"focusin":case"focusout":Nt=null;break;case"dragenter":case"dragleave":St=null;break;case"mouseover":case"mouseout":kt=null;break;case"pointerover":case"pointerout":nr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":rr.delete(t.pointerId)}}function Dn(e,t,n,r,l,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[l]},t!==null&&(t=jr(t),t!==null&&Ra(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function hf(e,t,n,r,l){switch(t){case"focusin":return Nt=Dn(Nt,e,t,n,r,l),!0;case"dragenter":return St=Dn(St,e,t,n,r,l),!0;case"mouseover":return kt=Dn(kt,e,t,n,r,l),!0;case"pointerover":var a=l.pointerId;return nr.set(a,Dn(nr.get(a)||null,e,t,n,r,l)),!0;case"gotpointercapture":return a=l.pointerId,rr.set(a,Dn(rr.get(a)||null,e,t,n,r,l)),!0}return!1}function _u(e){var t=Ut(e.target);if(t!==null){var n=Jt(t);if(n!==null){if(t=n.tag,t===13){if(t=mu(n),t!==null){e.blockedOn=t,Eu(e.priority,function(){ku(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=As(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ms=r,n.target.dispatchEvent(r),Ms=null}else return t=jr(n),t!==null&&Ra(t),e.blockedOn=n,!1;t.shift()}return!0}function Mi(e,t,n){Qr(e)&&n.delete(t)}function pf(){Bs=!1,Nt!==null&&Qr(Nt)&&(Nt=null),St!==null&&Qr(St)&&(St=null),kt!==null&&Qr(kt)&&(kt=null),nr.forEach(Mi),rr.forEach(Mi)}function zn(e,t){e.blockedOn===t&&(e.blockedOn=null,Bs||(Bs=!0,Le.unstable_scheduleCallback(Le.unstable_NormalPriority,pf)))}function lr(e){function t(l){return zn(l,e)}if(0<Tr.length){zn(Tr[0],e);for(var n=1;n<Tr.length;n++){var r=Tr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Nt!==null&&zn(Nt,e),St!==null&&zn(St,e),kt!==null&&zn(kt,e),nr.forEach(t),rr.forEach(t),n=0;n<xt.length;n++)r=xt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<xt.length&&(n=xt[0],n.blockedOn===null);)_u(n),n.blockedOn===null&&xt.shift()}var mn=ft.ReactCurrentBatchConfig,ul=!0;function mf(e,t,n,r){var l=H,a=mn.transition;mn.transition=null;try{H=1,Ta(e,t,n,r)}finally{H=l,mn.transition=a}}function vf(e,t,n,r){var l=H,a=mn.transition;mn.transition=null;try{H=4,Ta(e,t,n,r)}finally{H=l,mn.transition=a}}function Ta(e,t,n,r){if(ul){var l=As(e,t,n,r);if(l===null)us(e,t,r,cl,n),zi(e,r);else if(hf(l,e,t,n,r))r.stopPropagation();else if(zi(e,r),t&4&&-1<ff.indexOf(e)){for(;l!==null;){var a=jr(l);if(a!==null&&Su(a),a=As(e,t,n,r),a===null&&us(e,t,r,cl,n),a===l)break;l=a}l!==null&&r.stopPropagation()}else us(e,t,r,null,n)}}var cl=null;function As(e,t,n,r){if(cl=null,e=Pa(r),e=Ut(e),e!==null)if(t=Jt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=mu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return cl=e,null}function Pu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(nf()){case La:return 1;case yu:return 4;case il:case rf:return 16;case ju:return 536870912;default:return 16}default:return 16}}var jt=null,Da=null,Kr=null;function Lu(){if(Kr)return Kr;var e,t=Da,n=t.length,r,l="value"in jt?jt.value:jt.textContent,a=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===l[a-r];r++);return Kr=l.slice(e,1<r?1-r:void 0)}function Gr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Dr(){return!0}function $i(){return!1}function Re(e){function t(n,r,l,a,i){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=a,this.target=i,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(a):a[o]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?Dr:$i,this.isPropagationStopped=$i,this}return q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Dr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Dr)},persist:function(){},isPersistent:Dr}),t}var En={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},za=Re(En),yr=q({},En,{view:0,detail:0}),gf=Re(yr),es,ts,Mn,Rl=q({},yr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ma,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Mn&&(Mn&&e.type==="mousemove"?(es=e.screenX-Mn.screenX,ts=e.screenY-Mn.screenY):ts=es=0,Mn=e),es)},movementY:function(e){return"movementY"in e?e.movementY:ts}}),Ii=Re(Rl),xf=q({},Rl,{dataTransfer:0}),yf=Re(xf),jf=q({},yr,{relatedTarget:0}),ns=Re(jf),wf=q({},En,{animationName:0,elapsedTime:0,pseudoElement:0}),Nf=Re(wf),Sf=q({},En,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),kf=Re(Sf),Cf=q({},En,{data:0}),Oi=Re(Cf),Ef={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},_f={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Pf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Lf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Pf[e])?!!t[e]:!1}function Ma(){return Lf}var Ff=q({},yr,{key:function(e){if(e.key){var t=Ef[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Gr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?_f[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ma,charCode:function(e){return e.type==="keypress"?Gr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Gr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Rf=Re(Ff),Tf=q({},Rl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ui=Re(Tf),Df=q({},yr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ma}),zf=Re(Df),Mf=q({},En,{propertyName:0,elapsedTime:0,pseudoElement:0}),$f=Re(Mf),If=q({},Rl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Of=Re(If),Uf=[9,13,27,32],$a=it&&"CompositionEvent"in window,Qn=null;it&&"documentMode"in document&&(Qn=document.documentMode);var Bf=it&&"TextEvent"in window&&!Qn,Fu=it&&(!$a||Qn&&8<Qn&&11>=Qn),Bi=" ",Ai=!1;function Ru(e,t){switch(e){case"keyup":return Uf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var tn=!1;function Af(e,t){switch(e){case"compositionend":return Tu(t);case"keypress":return t.which!==32?null:(Ai=!0,Bi);case"textInput":return e=t.data,e===Bi&&Ai?null:e;default:return null}}function Hf(e,t){if(tn)return e==="compositionend"||!$a&&Ru(e,t)?(e=Lu(),Kr=Da=jt=null,tn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fu&&t.locale!=="ko"?null:t.data;default:return null}}var Wf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Wf[e.type]:t==="textarea"}function Du(e,t,n,r){cu(r),t=dl(t,"onChange"),0<t.length&&(n=new za("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kn=null,sr=null;function Vf(e){Vu(e,0)}function Tl(e){var t=ln(e);if(ru(t))return e}function bf(e,t){if(e==="change")return t}var zu=!1;if(it){var rs;if(it){var ls="oninput"in document;if(!ls){var Wi=document.createElement("div");Wi.setAttribute("oninput","return;"),ls=typeof Wi.oninput=="function"}rs=ls}else rs=!1;zu=rs&&(!document.documentMode||9<document.documentMode)}function Vi(){Kn&&(Kn.detachEvent("onpropertychange",Mu),sr=Kn=null)}function Mu(e){if(e.propertyName==="value"&&Tl(sr)){var t=[];Du(t,sr,e,Pa(e)),pu(Vf,t)}}function Qf(e,t,n){e==="focusin"?(Vi(),Kn=t,sr=n,Kn.attachEvent("onpropertychange",Mu)):e==="focusout"&&Vi()}function Kf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Tl(sr)}function Gf(e,t){if(e==="click")return Tl(t)}function Yf(e,t){if(e==="input"||e==="change")return Tl(t)}function Xf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qe=typeof Object.is=="function"?Object.is:Xf;function ar(e,t){if(Qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!Ss.call(t,l)||!Qe(e[l],t[l]))return!1}return!0}function bi(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Qi(e,t){var n=bi(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=bi(n)}}function $u(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?$u(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Iu(){for(var e=window,t=ll();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ll(e.document)}return t}function Ia(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Jf(e){var t=Iu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&$u(n.ownerDocument.documentElement,n)){if(r!==null&&Ia(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,a=Math.min(r.start,l);r=r.end===void 0?a:Math.min(r.end,l),!e.extend&&a>r&&(l=r,r=a,a=l),l=Qi(n,a);var i=Qi(n,r);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Zf=it&&"documentMode"in document&&11>=document.documentMode,nn=null,Hs=null,Gn=null,Ws=!1;function Ki(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ws||nn==null||nn!==ll(r)||(r=nn,"selectionStart"in r&&Ia(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gn&&ar(Gn,r)||(Gn=r,r=dl(Hs,"onSelect"),0<r.length&&(t=new za("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=nn)))}function zr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var rn={animationend:zr("Animation","AnimationEnd"),animationiteration:zr("Animation","AnimationIteration"),animationstart:zr("Animation","AnimationStart"),transitionend:zr("Transition","TransitionEnd")},ss={},Ou={};it&&(Ou=document.createElement("div").style,"AnimationEvent"in window||(delete rn.animationend.animation,delete rn.animationiteration.animation,delete rn.animationstart.animation),"TransitionEvent"in window||delete rn.transitionend.transition);function Dl(e){if(ss[e])return ss[e];if(!rn[e])return e;var t=rn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ou)return ss[e]=t[n];return e}var Uu=Dl("animationend"),Bu=Dl("animationiteration"),Au=Dl("animationstart"),Hu=Dl("transitionend"),Wu=new Map,Gi="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tt(e,t){Wu.set(e,t),Xt(t,[e])}for(var as=0;as<Gi.length;as++){var is=Gi[as],qf=is.toLowerCase(),eh=is[0].toUpperCase()+is.slice(1);Tt(qf,"on"+eh)}Tt(Uu,"onAnimationEnd");Tt(Bu,"onAnimationIteration");Tt(Au,"onAnimationStart");Tt("dblclick","onDoubleClick");Tt("focusin","onFocus");Tt("focusout","onBlur");Tt(Hu,"onTransitionEnd");xn("onMouseEnter",["mouseout","mouseover"]);xn("onMouseLeave",["mouseout","mouseover"]);xn("onPointerEnter",["pointerout","pointerover"]);xn("onPointerLeave",["pointerout","pointerover"]);Xt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Xt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Xt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Xt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Wn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),th=new Set("cancel close invalid load scroll toggle".split(" ").concat(Wn));function Yi(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Zd(r,t,void 0,e),e.currentTarget=null}function Vu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var o=r[i],u=o.instance,c=o.currentTarget;if(o=o.listener,u!==a&&l.isPropagationStopped())break e;Yi(l,o,c),a=u}else for(i=0;i<r.length;i++){if(o=r[i],u=o.instance,c=o.currentTarget,o=o.listener,u!==a&&l.isPropagationStopped())break e;Yi(l,o,c),a=u}}}if(al)throw e=Os,al=!1,Os=null,e}function V(e,t){var n=t[Gs];n===void 0&&(n=t[Gs]=new Set);var r=e+"__bubble";n.has(r)||(bu(t,e,2,!1),n.add(r))}function os(e,t,n){var r=0;t&&(r|=4),bu(n,e,r,t)}var Mr="_reactListening"+Math.random().toString(36).slice(2);function ir(e){if(!e[Mr]){e[Mr]=!0,Zo.forEach(function(n){n!=="selectionchange"&&(th.has(n)||os(n,!1,e),os(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Mr]||(t[Mr]=!0,os("selectionchange",!1,t))}}function bu(e,t,n,r){switch(Pu(t)){case 1:var l=mf;break;case 4:l=vf;break;default:l=Ta}n=l.bind(null,t,n,e),l=void 0,!Is||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function us(e,t,n,r,l){var a=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var o=r.stateNode.containerInfo;if(o===l||o.nodeType===8&&o.parentNode===l)break;if(i===4)for(i=r.return;i!==null;){var u=i.tag;if((u===3||u===4)&&(u=i.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;i=i.return}for(;o!==null;){if(i=Ut(o),i===null)return;if(u=i.tag,u===5||u===6){r=a=i;continue e}o=o.parentNode}}r=r.return}pu(function(){var c=a,f=Pa(n),v=[];e:{var h=Wu.get(e);if(h!==void 0){var S=za,N=e;switch(e){case"keypress":if(Gr(n)===0)break e;case"keydown":case"keyup":S=Rf;break;case"focusin":N="focus",S=ns;break;case"focusout":N="blur",S=ns;break;case"beforeblur":case"afterblur":S=ns;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=Ii;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=yf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=zf;break;case Uu:case Bu:case Au:S=Nf;break;case Hu:S=$f;break;case"scroll":S=gf;break;case"wheel":S=Of;break;case"copy":case"cut":case"paste":S=kf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=Ui}var _=(t&4)!==0,L=!_&&e==="scroll",p=_?h!==null?h+"Capture":null:h;_=[];for(var d=c,m;d!==null;){m=d;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,p!==null&&(w=tr(d,p),w!=null&&_.push(or(d,w,m)))),L)break;d=d.return}0<_.length&&(h=new S(h,N,null,n,f),v.push({event:h,listeners:_}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",h&&n!==Ms&&(N=n.relatedTarget||n.fromElement)&&(Ut(N)||N[ot]))break e;if((S||h)&&(h=f.window===f?f:(h=f.ownerDocument)?h.defaultView||h.parentWindow:window,S?(N=n.relatedTarget||n.toElement,S=c,N=N?Ut(N):null,N!==null&&(L=Jt(N),N!==L||N.tag!==5&&N.tag!==6)&&(N=null)):(S=null,N=c),S!==N)){if(_=Ii,w="onMouseLeave",p="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(_=Ui,w="onPointerLeave",p="onPointerEnter",d="pointer"),L=S==null?h:ln(S),m=N==null?h:ln(N),h=new _(w,d+"leave",S,n,f),h.target=L,h.relatedTarget=m,w=null,Ut(f)===c&&(_=new _(p,d+"enter",N,n,f),_.target=m,_.relatedTarget=L,w=_),L=w,S&&N)t:{for(_=S,p=N,d=0,m=_;m;m=Zt(m))d++;for(m=0,w=p;w;w=Zt(w))m++;for(;0<d-m;)_=Zt(_),d--;for(;0<m-d;)p=Zt(p),m--;for(;d--;){if(_===p||p!==null&&_===p.alternate)break t;_=Zt(_),p=Zt(p)}_=null}else _=null;S!==null&&Xi(v,h,S,_,!1),N!==null&&L!==null&&Xi(v,L,N,_,!0)}}e:{if(h=c?ln(c):window,S=h.nodeName&&h.nodeName.toLowerCase(),S==="select"||S==="input"&&h.type==="file")var x=bf;else if(Hi(h))if(zu)x=Yf;else{x=Kf;var E=Qf}else(S=h.nodeName)&&S.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(x=Gf);if(x&&(x=x(e,c))){Du(v,x,n,f);break e}E&&E(e,h,c),e==="focusout"&&(E=h._wrapperState)&&E.controlled&&h.type==="number"&&Fs(h,"number",h.value)}switch(E=c?ln(c):window,e){case"focusin":(Hi(E)||E.contentEditable==="true")&&(nn=E,Hs=c,Gn=null);break;case"focusout":Gn=Hs=nn=null;break;case"mousedown":Ws=!0;break;case"contextmenu":case"mouseup":case"dragend":Ws=!1,Ki(v,n,f);break;case"selectionchange":if(Zf)break;case"keydown":case"keyup":Ki(v,n,f)}var P;if($a)e:{switch(e){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else tn?Ru(e,n)&&(j="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Fu&&n.locale!=="ko"&&(tn||j!=="onCompositionStart"?j==="onCompositionEnd"&&tn&&(P=Lu()):(jt=f,Da="value"in jt?jt.value:jt.textContent,tn=!0)),E=dl(c,j),0<E.length&&(j=new Oi(j,e,null,n,f),v.push({event:j,listeners:E}),P?j.data=P:(P=Tu(n),P!==null&&(j.data=P)))),(P=Bf?Af(e,n):Hf(e,n))&&(c=dl(c,"onBeforeInput"),0<c.length&&(f=new Oi("onBeforeInput","beforeinput",null,n,f),v.push({event:f,listeners:c}),f.data=P))}Vu(v,t)})}function or(e,t,n){return{instance:e,listener:t,currentTarget:n}}function dl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,a=l.stateNode;l.tag===5&&a!==null&&(l=a,a=tr(e,n),a!=null&&r.unshift(or(e,a,l)),a=tr(e,t),a!=null&&r.push(or(e,a,l))),e=e.return}return r}function Zt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Xi(e,t,n,r,l){for(var a=t._reactName,i=[];n!==null&&n!==r;){var o=n,u=o.alternate,c=o.stateNode;if(u!==null&&u===r)break;o.tag===5&&c!==null&&(o=c,l?(u=tr(n,a),u!=null&&i.unshift(or(n,u,o))):l||(u=tr(n,a),u!=null&&i.push(or(n,u,o)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var nh=/\r\n?/g,rh=/\u0000|\uFFFD/g;function Ji(e){return(typeof e=="string"?e:""+e).replace(nh,`
`).replace(rh,"")}function $r(e,t,n){if(t=Ji(t),Ji(e)!==t&&n)throw Error(F(425))}function fl(){}var Vs=null,bs=null;function Qs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ks=typeof setTimeout=="function"?setTimeout:void 0,lh=typeof clearTimeout=="function"?clearTimeout:void 0,Zi=typeof Promise=="function"?Promise:void 0,sh=typeof queueMicrotask=="function"?queueMicrotask:typeof Zi<"u"?function(e){return Zi.resolve(null).then(e).catch(ah)}:Ks;function ah(e){setTimeout(function(){throw e})}function cs(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),lr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);lr(t)}function Ct(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qi(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var _n=Math.random().toString(36).slice(2),Je="__reactFiber$"+_n,ur="__reactProps$"+_n,ot="__reactContainer$"+_n,Gs="__reactEvents$"+_n,ih="__reactListeners$"+_n,oh="__reactHandles$"+_n;function Ut(e){var t=e[Je];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ot]||n[Je]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qi(e);e!==null;){if(n=e[Je])return n;e=qi(e)}return t}e=n,n=e.parentNode}return null}function jr(e){return e=e[Je]||e[ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function ln(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(F(33))}function zl(e){return e[ur]||null}var Ys=[],sn=-1;function Dt(e){return{current:e}}function b(e){0>sn||(e.current=Ys[sn],Ys[sn]=null,sn--)}function W(e,t){sn++,Ys[sn]=e.current,e.current=t}var Rt={},ve=Dt(Rt),Se=Dt(!1),Vt=Rt;function yn(e,t){var n=e.type.contextTypes;if(!n)return Rt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},a;for(a in n)l[a]=t[a];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function ke(e){return e=e.childContextTypes,e!=null}function hl(){b(Se),b(ve)}function eo(e,t,n){if(ve.current!==Rt)throw Error(F(168));W(ve,t),W(Se,n)}function Qu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(F(108,bd(e)||"Unknown",l));return q({},n,r)}function pl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Rt,Vt=ve.current,W(ve,e),W(Se,Se.current),!0}function to(e,t,n){var r=e.stateNode;if(!r)throw Error(F(169));n?(e=Qu(e,t,Vt),r.__reactInternalMemoizedMergedChildContext=e,b(Se),b(ve),W(ve,e)):b(Se),W(Se,n)}var nt=null,Ml=!1,ds=!1;function Ku(e){nt===null?nt=[e]:nt.push(e)}function uh(e){Ml=!0,Ku(e)}function zt(){if(!ds&&nt!==null){ds=!0;var e=0,t=H;try{var n=nt;for(H=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}nt=null,Ml=!1}catch(l){throw nt!==null&&(nt=nt.slice(e+1)),xu(La,zt),l}finally{H=t,ds=!1}}return null}var an=[],on=0,ml=null,vl=0,Te=[],De=0,bt=null,rt=1,lt="";function $t(e,t){an[on++]=vl,an[on++]=ml,ml=e,vl=t}function Gu(e,t,n){Te[De++]=rt,Te[De++]=lt,Te[De++]=bt,bt=e;var r=rt;e=lt;var l=32-Ve(r)-1;r&=~(1<<l),n+=1;var a=32-Ve(t)+l;if(30<a){var i=l-l%5;a=(r&(1<<i)-1).toString(32),r>>=i,l-=i,rt=1<<32-Ve(t)+l|n<<l|r,lt=a+e}else rt=1<<a|n<<l|r,lt=e}function Oa(e){e.return!==null&&($t(e,1),Gu(e,1,0))}function Ua(e){for(;e===ml;)ml=an[--on],an[on]=null,vl=an[--on],an[on]=null;for(;e===bt;)bt=Te[--De],Te[De]=null,lt=Te[--De],Te[De]=null,rt=Te[--De],Te[De]=null}var Pe=null,_e=null,K=!1,He=null;function Yu(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function no(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Pe=e,_e=Ct(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Pe=e,_e=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=bt!==null?{id:rt,overflow:lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Pe=e,_e=null,!0):!1;default:return!1}}function Xs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Js(e){if(K){var t=_e;if(t){var n=t;if(!no(e,t)){if(Xs(e))throw Error(F(418));t=Ct(n.nextSibling);var r=Pe;t&&no(e,t)?Yu(r,n):(e.flags=e.flags&-4097|2,K=!1,Pe=e)}}else{if(Xs(e))throw Error(F(418));e.flags=e.flags&-4097|2,K=!1,Pe=e}}}function ro(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Pe=e}function Ir(e){if(e!==Pe)return!1;if(!K)return ro(e),K=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Qs(e.type,e.memoizedProps)),t&&(t=_e)){if(Xs(e))throw Xu(),Error(F(418));for(;t;)Yu(e,t),t=Ct(t.nextSibling)}if(ro(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(F(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){_e=Ct(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}_e=null}}else _e=Pe?Ct(e.stateNode.nextSibling):null;return!0}function Xu(){for(var e=_e;e;)e=Ct(e.nextSibling)}function jn(){_e=Pe=null,K=!1}function Ba(e){He===null?He=[e]:He.push(e)}var ch=ft.ReactCurrentBatchConfig;function $n(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(F(309));var r=n.stateNode}if(!r)throw Error(F(147,e));var l=r,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(i){var o=l.refs;i===null?delete o[a]:o[a]=i},t._stringRef=a,t)}if(typeof e!="string")throw Error(F(284));if(!n._owner)throw Error(F(290,e))}return e}function Or(e,t){throw e=Object.prototype.toString.call(t),Error(F(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function lo(e){var t=e._init;return t(e._payload)}function Ju(e){function t(p,d){if(e){var m=p.deletions;m===null?(p.deletions=[d],p.flags|=16):m.push(d)}}function n(p,d){if(!e)return null;for(;d!==null;)t(p,d),d=d.sibling;return null}function r(p,d){for(p=new Map;d!==null;)d.key!==null?p.set(d.key,d):p.set(d.index,d),d=d.sibling;return p}function l(p,d){return p=Lt(p,d),p.index=0,p.sibling=null,p}function a(p,d,m){return p.index=m,e?(m=p.alternate,m!==null?(m=m.index,m<d?(p.flags|=2,d):m):(p.flags|=2,d)):(p.flags|=1048576,d)}function i(p){return e&&p.alternate===null&&(p.flags|=2),p}function o(p,d,m,w){return d===null||d.tag!==6?(d=xs(m,p.mode,w),d.return=p,d):(d=l(d,m),d.return=p,d)}function u(p,d,m,w){var x=m.type;return x===en?f(p,d,m.props.children,w,m.key):d!==null&&(d.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===vt&&lo(x)===d.type)?(w=l(d,m.props),w.ref=$n(p,d,m),w.return=p,w):(w=tl(m.type,m.key,m.props,null,p.mode,w),w.ref=$n(p,d,m),w.return=p,w)}function c(p,d,m,w){return d===null||d.tag!==4||d.stateNode.containerInfo!==m.containerInfo||d.stateNode.implementation!==m.implementation?(d=ys(m,p.mode,w),d.return=p,d):(d=l(d,m.children||[]),d.return=p,d)}function f(p,d,m,w,x){return d===null||d.tag!==7?(d=Wt(m,p.mode,w,x),d.return=p,d):(d=l(d,m),d.return=p,d)}function v(p,d,m){if(typeof d=="string"&&d!==""||typeof d=="number")return d=xs(""+d,p.mode,m),d.return=p,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case _r:return m=tl(d.type,d.key,d.props,null,p.mode,m),m.ref=$n(p,null,d),m.return=p,m;case qt:return d=ys(d,p.mode,m),d.return=p,d;case vt:var w=d._init;return v(p,w(d._payload),m)}if(An(d)||Rn(d))return d=Wt(d,p.mode,m,null),d.return=p,d;Or(p,d)}return null}function h(p,d,m,w){var x=d!==null?d.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return x!==null?null:o(p,d,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case _r:return m.key===x?u(p,d,m,w):null;case qt:return m.key===x?c(p,d,m,w):null;case vt:return x=m._init,h(p,d,x(m._payload),w)}if(An(m)||Rn(m))return x!==null?null:f(p,d,m,w,null);Or(p,m)}return null}function S(p,d,m,w,x){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(m)||null,o(d,p,""+w,x);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case _r:return p=p.get(w.key===null?m:w.key)||null,u(d,p,w,x);case qt:return p=p.get(w.key===null?m:w.key)||null,c(d,p,w,x);case vt:var E=w._init;return S(p,d,m,E(w._payload),x)}if(An(w)||Rn(w))return p=p.get(m)||null,f(d,p,w,x,null);Or(d,w)}return null}function N(p,d,m,w){for(var x=null,E=null,P=d,j=d=0,C=null;P!==null&&j<m.length;j++){P.index>j?(C=P,P=null):C=P.sibling;var y=h(p,P,m[j],w);if(y===null){P===null&&(P=C);break}e&&P&&y.alternate===null&&t(p,P),d=a(y,d,j),E===null?x=y:E.sibling=y,E=y,P=C}if(j===m.length)return n(p,P),K&&$t(p,j),x;if(P===null){for(;j<m.length;j++)P=v(p,m[j],w),P!==null&&(d=a(P,d,j),E===null?x=P:E.sibling=P,E=P);return K&&$t(p,j),x}for(P=r(p,P);j<m.length;j++)C=S(P,p,j,m[j],w),C!==null&&(e&&C.alternate!==null&&P.delete(C.key===null?j:C.key),d=a(C,d,j),E===null?x=C:E.sibling=C,E=C);return e&&P.forEach(function(k){return t(p,k)}),K&&$t(p,j),x}function _(p,d,m,w){var x=Rn(m);if(typeof x!="function")throw Error(F(150));if(m=x.call(m),m==null)throw Error(F(151));for(var E=x=null,P=d,j=d=0,C=null,y=m.next();P!==null&&!y.done;j++,y=m.next()){P.index>j?(C=P,P=null):C=P.sibling;var k=h(p,P,y.value,w);if(k===null){P===null&&(P=C);break}e&&P&&k.alternate===null&&t(p,P),d=a(k,d,j),E===null?x=k:E.sibling=k,E=k,P=C}if(y.done)return n(p,P),K&&$t(p,j),x;if(P===null){for(;!y.done;j++,y=m.next())y=v(p,y.value,w),y!==null&&(d=a(y,d,j),E===null?x=y:E.sibling=y,E=y);return K&&$t(p,j),x}for(P=r(p,P);!y.done;j++,y=m.next())y=S(P,p,j,y.value,w),y!==null&&(e&&y.alternate!==null&&P.delete(y.key===null?j:y.key),d=a(y,d,j),E===null?x=y:E.sibling=y,E=y);return e&&P.forEach(function(T){return t(p,T)}),K&&$t(p,j),x}function L(p,d,m,w){if(typeof m=="object"&&m!==null&&m.type===en&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case _r:e:{for(var x=m.key,E=d;E!==null;){if(E.key===x){if(x=m.type,x===en){if(E.tag===7){n(p,E.sibling),d=l(E,m.props.children),d.return=p,p=d;break e}}else if(E.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===vt&&lo(x)===E.type){n(p,E.sibling),d=l(E,m.props),d.ref=$n(p,E,m),d.return=p,p=d;break e}n(p,E);break}else t(p,E);E=E.sibling}m.type===en?(d=Wt(m.props.children,p.mode,w,m.key),d.return=p,p=d):(w=tl(m.type,m.key,m.props,null,p.mode,w),w.ref=$n(p,d,m),w.return=p,p=w)}return i(p);case qt:e:{for(E=m.key;d!==null;){if(d.key===E)if(d.tag===4&&d.stateNode.containerInfo===m.containerInfo&&d.stateNode.implementation===m.implementation){n(p,d.sibling),d=l(d,m.children||[]),d.return=p,p=d;break e}else{n(p,d);break}else t(p,d);d=d.sibling}d=ys(m,p.mode,w),d.return=p,p=d}return i(p);case vt:return E=m._init,L(p,d,E(m._payload),w)}if(An(m))return N(p,d,m,w);if(Rn(m))return _(p,d,m,w);Or(p,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,d!==null&&d.tag===6?(n(p,d.sibling),d=l(d,m),d.return=p,p=d):(n(p,d),d=xs(m,p.mode,w),d.return=p,p=d),i(p)):n(p,d)}return L}var wn=Ju(!0),Zu=Ju(!1),gl=Dt(null),xl=null,un=null,Aa=null;function Ha(){Aa=un=xl=null}function Wa(e){var t=gl.current;b(gl),e._currentValue=t}function Zs(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function vn(e,t){xl=e,Aa=un=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ne=!0),e.firstContext=null)}function Ie(e){var t=e._currentValue;if(Aa!==e)if(e={context:e,memoizedValue:t,next:null},un===null){if(xl===null)throw Error(F(308));un=e,xl.dependencies={lanes:0,firstContext:e}}else un=un.next=e;return t}var Bt=null;function Va(e){Bt===null?Bt=[e]:Bt.push(e)}function qu(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Va(t)):(n.next=l.next,l.next=n),t.interleaved=n,ut(e,r)}function ut(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gt=!1;function ba(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ec(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function st(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Et(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,A&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,ut(e,n)}return l=r.interleaved,l===null?(t.next=t,Va(r)):(t.next=l.next,l.next=t),r.interleaved=t,ut(e,n)}function Yr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fa(e,n)}}function so(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};a===null?l=a=i:a=a.next=i,n=n.next}while(n!==null);a===null?l=a=t:a=a.next=t}else l=a=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function yl(e,t,n,r){var l=e.updateQueue;gt=!1;var a=l.firstBaseUpdate,i=l.lastBaseUpdate,o=l.shared.pending;if(o!==null){l.shared.pending=null;var u=o,c=u.next;u.next=null,i===null?a=c:i.next=c,i=u;var f=e.alternate;f!==null&&(f=f.updateQueue,o=f.lastBaseUpdate,o!==i&&(o===null?f.firstBaseUpdate=c:o.next=c,f.lastBaseUpdate=u))}if(a!==null){var v=l.baseState;i=0,f=c=u=null,o=a;do{var h=o.lane,S=o.eventTime;if((r&h)===h){f!==null&&(f=f.next={eventTime:S,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var N=e,_=o;switch(h=t,S=n,_.tag){case 1:if(N=_.payload,typeof N=="function"){v=N.call(S,v,h);break e}v=N;break e;case 3:N.flags=N.flags&-65537|128;case 0:if(N=_.payload,h=typeof N=="function"?N.call(S,v,h):N,h==null)break e;v=q({},v,h);break e;case 2:gt=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,h=l.effects,h===null?l.effects=[o]:h.push(o))}else S={eventTime:S,lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},f===null?(c=f=S,u=v):f=f.next=S,i|=h;if(o=o.next,o===null){if(o=l.shared.pending,o===null)break;h=o,o=h.next,h.next=null,l.lastBaseUpdate=h,l.shared.pending=null}}while(!0);if(f===null&&(u=v),l.baseState=u,l.firstBaseUpdate=c,l.lastBaseUpdate=f,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else a===null&&(l.shared.lanes=0);Kt|=i,e.lanes=i,e.memoizedState=v}}function ao(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(F(191,l));l.call(r)}}}var wr={},qe=Dt(wr),cr=Dt(wr),dr=Dt(wr);function At(e){if(e===wr)throw Error(F(174));return e}function Qa(e,t){switch(W(dr,t),W(cr,e),W(qe,wr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ts(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ts(t,e)}b(qe),W(qe,t)}function Nn(){b(qe),b(cr),b(dr)}function tc(e){At(dr.current);var t=At(qe.current),n=Ts(t,e.type);t!==n&&(W(cr,e),W(qe,n))}function Ka(e){cr.current===e&&(b(qe),b(cr))}var J=Dt(0);function jl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var fs=[];function Ga(){for(var e=0;e<fs.length;e++)fs[e]._workInProgressVersionPrimary=null;fs.length=0}var Xr=ft.ReactCurrentDispatcher,hs=ft.ReactCurrentBatchConfig,Qt=0,Z=null,le=null,ae=null,wl=!1,Yn=!1,fr=0,dh=0;function he(){throw Error(F(321))}function Ya(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qe(e[n],t[n]))return!1;return!0}function Xa(e,t,n,r,l,a){if(Qt=a,Z=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xr.current=e===null||e.memoizedState===null?mh:vh,e=n(r,l),Yn){a=0;do{if(Yn=!1,fr=0,25<=a)throw Error(F(301));a+=1,ae=le=null,t.updateQueue=null,Xr.current=gh,e=n(r,l)}while(Yn)}if(Xr.current=Nl,t=le!==null&&le.next!==null,Qt=0,ae=le=Z=null,wl=!1,t)throw Error(F(300));return e}function Ja(){var e=fr!==0;return fr=0,e}function Xe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ae===null?Z.memoizedState=ae=e:ae=ae.next=e,ae}function Oe(){if(le===null){var e=Z.alternate;e=e!==null?e.memoizedState:null}else e=le.next;var t=ae===null?Z.memoizedState:ae.next;if(t!==null)ae=t,le=e;else{if(e===null)throw Error(F(310));le=e,e={memoizedState:le.memoizedState,baseState:le.baseState,baseQueue:le.baseQueue,queue:le.queue,next:null},ae===null?Z.memoizedState=ae=e:ae=ae.next=e}return ae}function hr(e,t){return typeof t=="function"?t(e):t}function ps(e){var t=Oe(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=le,l=r.baseQueue,a=n.pending;if(a!==null){if(l!==null){var i=l.next;l.next=a.next,a.next=i}r.baseQueue=l=a,n.pending=null}if(l!==null){a=l.next,r=r.baseState;var o=i=null,u=null,c=a;do{var f=c.lane;if((Qt&f)===f)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var v={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(o=u=v,i=r):u=u.next=v,Z.lanes|=f,Kt|=f}c=c.next}while(c!==null&&c!==a);u===null?i=r:u.next=o,Qe(r,t.memoizedState)||(Ne=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do a=l.lane,Z.lanes|=a,Kt|=a,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ms(e){var t=Oe(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,a=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do a=e(a,i.action),i=i.next;while(i!==l);Qe(a,t.memoizedState)||(Ne=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function nc(){}function rc(e,t){var n=Z,r=Oe(),l=t(),a=!Qe(r.memoizedState,l);if(a&&(r.memoizedState=l,Ne=!0),r=r.queue,Za(ac.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||ae!==null&&ae.memoizedState.tag&1){if(n.flags|=2048,pr(9,sc.bind(null,n,r,l,t),void 0,null),ie===null)throw Error(F(349));Qt&30||lc(n,t,l)}return l}function lc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function sc(e,t,n,r){t.value=n,t.getSnapshot=r,ic(t)&&oc(e)}function ac(e,t,n){return n(function(){ic(t)&&oc(e)})}function ic(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qe(e,n)}catch{return!0}}function oc(e){var t=ut(e,1);t!==null&&be(t,e,1,-1)}function io(e){var t=Xe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:hr,lastRenderedState:e},t.queue=e,e=e.dispatch=ph.bind(null,Z,e),[t.memoizedState,e]}function pr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function uc(){return Oe().memoizedState}function Jr(e,t,n,r){var l=Xe();Z.flags|=e,l.memoizedState=pr(1|t,n,void 0,r===void 0?null:r)}function $l(e,t,n,r){var l=Oe();r=r===void 0?null:r;var a=void 0;if(le!==null){var i=le.memoizedState;if(a=i.destroy,r!==null&&Ya(r,i.deps)){l.memoizedState=pr(t,n,a,r);return}}Z.flags|=e,l.memoizedState=pr(1|t,n,a,r)}function oo(e,t){return Jr(8390656,8,e,t)}function Za(e,t){return $l(2048,8,e,t)}function cc(e,t){return $l(4,2,e,t)}function dc(e,t){return $l(4,4,e,t)}function fc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hc(e,t,n){return n=n!=null?n.concat([e]):null,$l(4,4,fc.bind(null,t,e),n)}function qa(){}function pc(e,t){var n=Oe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ya(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function mc(e,t){var n=Oe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ya(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function vc(e,t,n){return Qt&21?(Qe(n,t)||(n=wu(),Z.lanes|=n,Kt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ne=!0),e.memoizedState=n)}function fh(e,t){var n=H;H=n!==0&&4>n?n:4,e(!0);var r=hs.transition;hs.transition={};try{e(!1),t()}finally{H=n,hs.transition=r}}function gc(){return Oe().memoizedState}function hh(e,t,n){var r=Pt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},xc(e))yc(t,n);else if(n=qu(e,t,n,r),n!==null){var l=xe();be(n,e,r,l),jc(n,t,r)}}function ph(e,t,n){var r=Pt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(xc(e))yc(t,l);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var i=t.lastRenderedState,o=a(i,n);if(l.hasEagerState=!0,l.eagerState=o,Qe(o,i)){var u=t.interleaved;u===null?(l.next=l,Va(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=qu(e,t,l,r),n!==null&&(l=xe(),be(n,e,r,l),jc(n,t,r))}}function xc(e){var t=e.alternate;return e===Z||t!==null&&t===Z}function yc(e,t){Yn=wl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function jc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fa(e,n)}}var Nl={readContext:Ie,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useInsertionEffect:he,useLayoutEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useMutableSource:he,useSyncExternalStore:he,useId:he,unstable_isNewReconciler:!1},mh={readContext:Ie,useCallback:function(e,t){return Xe().memoizedState=[e,t===void 0?null:t],e},useContext:Ie,useEffect:oo,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Jr(4194308,4,fc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Jr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Jr(4,2,e,t)},useMemo:function(e,t){var n=Xe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Xe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=hh.bind(null,Z,e),[r.memoizedState,e]},useRef:function(e){var t=Xe();return e={current:e},t.memoizedState=e},useState:io,useDebugValue:qa,useDeferredValue:function(e){return Xe().memoizedState=e},useTransition:function(){var e=io(!1),t=e[0];return e=fh.bind(null,e[1]),Xe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Z,l=Xe();if(K){if(n===void 0)throw Error(F(407));n=n()}else{if(n=t(),ie===null)throw Error(F(349));Qt&30||lc(r,t,n)}l.memoizedState=n;var a={value:n,getSnapshot:t};return l.queue=a,oo(ac.bind(null,r,a,e),[e]),r.flags|=2048,pr(9,sc.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=Xe(),t=ie.identifierPrefix;if(K){var n=lt,r=rt;n=(r&~(1<<32-Ve(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=fr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=dh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},vh={readContext:Ie,useCallback:pc,useContext:Ie,useEffect:Za,useImperativeHandle:hc,useInsertionEffect:cc,useLayoutEffect:dc,useMemo:mc,useReducer:ps,useRef:uc,useState:function(){return ps(hr)},useDebugValue:qa,useDeferredValue:function(e){var t=Oe();return vc(t,le.memoizedState,e)},useTransition:function(){var e=ps(hr)[0],t=Oe().memoizedState;return[e,t]},useMutableSource:nc,useSyncExternalStore:rc,useId:gc,unstable_isNewReconciler:!1},gh={readContext:Ie,useCallback:pc,useContext:Ie,useEffect:Za,useImperativeHandle:hc,useInsertionEffect:cc,useLayoutEffect:dc,useMemo:mc,useReducer:ms,useRef:uc,useState:function(){return ms(hr)},useDebugValue:qa,useDeferredValue:function(e){var t=Oe();return le===null?t.memoizedState=e:vc(t,le.memoizedState,e)},useTransition:function(){var e=ms(hr)[0],t=Oe().memoizedState;return[e,t]},useMutableSource:nc,useSyncExternalStore:rc,useId:gc,unstable_isNewReconciler:!1};function Be(e,t){if(e&&e.defaultProps){t=q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function qs(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Il={isMounted:function(e){return(e=e._reactInternals)?Jt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),l=Pt(e),a=st(r,l);a.payload=t,n!=null&&(a.callback=n),t=Et(e,a,l),t!==null&&(be(t,e,l,r),Yr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),l=Pt(e),a=st(r,l);a.tag=1,a.payload=t,n!=null&&(a.callback=n),t=Et(e,a,l),t!==null&&(be(t,e,l,r),Yr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=Pt(e),l=st(n,r);l.tag=2,t!=null&&(l.callback=t),t=Et(e,l,r),t!==null&&(be(t,e,r,n),Yr(t,e,r))}};function uo(e,t,n,r,l,a,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,a,i):t.prototype&&t.prototype.isPureReactComponent?!ar(n,r)||!ar(l,a):!0}function wc(e,t,n){var r=!1,l=Rt,a=t.contextType;return typeof a=="object"&&a!==null?a=Ie(a):(l=ke(t)?Vt:ve.current,r=t.contextTypes,a=(r=r!=null)?yn(e,l):Rt),t=new t(n,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Il,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=a),t}function co(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Il.enqueueReplaceState(t,t.state,null)}function ea(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},ba(e);var a=t.contextType;typeof a=="object"&&a!==null?l.context=Ie(a):(a=ke(t)?Vt:ve.current,l.context=yn(e,a)),l.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(qs(e,t,a,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Il.enqueueReplaceState(l,l.state,null),yl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Sn(e,t){try{var n="",r=t;do n+=Vd(r),r=r.return;while(r);var l=n}catch(a){l=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:l,digest:null}}function vs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ta(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var xh=typeof WeakMap=="function"?WeakMap:Map;function Nc(e,t,n){n=st(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){kl||(kl=!0,da=r),ta(e,t)},n}function Sc(e,t,n){n=st(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){ta(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(n.callback=function(){ta(e,t),typeof r!="function"&&(_t===null?_t=new Set([this]):_t.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function fo(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new xh;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Th.bind(null,e,t,n),t.then(e,e))}function ho(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function po(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=st(-1,1),t.tag=2,Et(n,t,1))),n.lanes|=1),e)}var yh=ft.ReactCurrentOwner,Ne=!1;function ge(e,t,n,r){t.child=e===null?Zu(t,null,n,r):wn(t,e.child,n,r)}function mo(e,t,n,r,l){n=n.render;var a=t.ref;return vn(t,l),r=Xa(e,t,n,r,a,l),n=Ja(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,ct(e,t,l)):(K&&n&&Oa(t),t.flags|=1,ge(e,t,r,l),t.child)}function vo(e,t,n,r,l){if(e===null){var a=n.type;return typeof a=="function"&&!ii(a)&&a.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=a,kc(e,t,a,r,l)):(e=tl(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!(e.lanes&l)){var i=a.memoizedProps;if(n=n.compare,n=n!==null?n:ar,n(i,r)&&e.ref===t.ref)return ct(e,t,l)}return t.flags|=1,e=Lt(a,r),e.ref=t.ref,e.return=t,t.child=e}function kc(e,t,n,r,l){if(e!==null){var a=e.memoizedProps;if(ar(a,r)&&e.ref===t.ref)if(Ne=!1,t.pendingProps=r=a,(e.lanes&l)!==0)e.flags&131072&&(Ne=!0);else return t.lanes=e.lanes,ct(e,t,l)}return na(e,t,n,r,l)}function Cc(e,t,n){var r=t.pendingProps,l=r.children,a=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(dn,Ee),Ee|=n;else{if(!(n&1073741824))return e=a!==null?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(dn,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=a!==null?a.baseLanes:n,W(dn,Ee),Ee|=r}else a!==null?(r=a.baseLanes|n,t.memoizedState=null):r=n,W(dn,Ee),Ee|=r;return ge(e,t,l,n),t.child}function Ec(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function na(e,t,n,r,l){var a=ke(n)?Vt:ve.current;return a=yn(t,a),vn(t,l),n=Xa(e,t,n,r,a,l),r=Ja(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,ct(e,t,l)):(K&&r&&Oa(t),t.flags|=1,ge(e,t,n,l),t.child)}function go(e,t,n,r,l){if(ke(n)){var a=!0;pl(t)}else a=!1;if(vn(t,l),t.stateNode===null)Zr(e,t),wc(t,n,r),ea(t,n,r,l),r=!0;else if(e===null){var i=t.stateNode,o=t.memoizedProps;i.props=o;var u=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=Ie(c):(c=ke(n)?Vt:ve.current,c=yn(t,c));var f=n.getDerivedStateFromProps,v=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";v||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==r||u!==c)&&co(t,i,r,c),gt=!1;var h=t.memoizedState;i.state=h,yl(t,r,i,l),u=t.memoizedState,o!==r||h!==u||Se.current||gt?(typeof f=="function"&&(qs(t,n,f,r),u=t.memoizedState),(o=gt||uo(t,n,o,r,h,u,c))?(v||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),i.props=r,i.state=u,i.context=c,r=o):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,ec(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:Be(t.type,o),i.props=c,v=t.pendingProps,h=i.context,u=n.contextType,typeof u=="object"&&u!==null?u=Ie(u):(u=ke(n)?Vt:ve.current,u=yn(t,u));var S=n.getDerivedStateFromProps;(f=typeof S=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==v||h!==u)&&co(t,i,r,u),gt=!1,h=t.memoizedState,i.state=h,yl(t,r,i,l);var N=t.memoizedState;o!==v||h!==N||Se.current||gt?(typeof S=="function"&&(qs(t,n,S,r),N=t.memoizedState),(c=gt||uo(t,n,c,r,h,N,u)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,N,u),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,N,u)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=N),i.props=r,i.state=N,i.context=u,r=c):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return ra(e,t,n,r,a,l)}function ra(e,t,n,r,l,a){Ec(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return l&&to(t,n,!1),ct(e,t,a);r=t.stateNode,yh.current=t;var o=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=wn(t,e.child,null,a),t.child=wn(t,null,o,a)):ge(e,t,o,a),t.memoizedState=r.state,l&&to(t,n,!0),t.child}function _c(e){var t=e.stateNode;t.pendingContext?eo(e,t.pendingContext,t.pendingContext!==t.context):t.context&&eo(e,t.context,!1),Qa(e,t.containerInfo)}function xo(e,t,n,r,l){return jn(),Ba(l),t.flags|=256,ge(e,t,n,r),t.child}var la={dehydrated:null,treeContext:null,retryLane:0};function sa(e){return{baseLanes:e,cachePool:null,transitions:null}}function Pc(e,t,n){var r=t.pendingProps,l=J.current,a=!1,i=(t.flags&128)!==0,o;if((o=i)||(o=e!==null&&e.memoizedState===null?!1:(l&2)!==0),o?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),W(J,l&1),e===null)return Js(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,a?(r=t.mode,a=t.child,i={mode:"hidden",children:i},!(r&1)&&a!==null?(a.childLanes=0,a.pendingProps=i):a=Bl(i,r,0,null),e=Wt(e,r,n,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=sa(n),t.memoizedState=la,e):ei(t,i));if(l=e.memoizedState,l!==null&&(o=l.dehydrated,o!==null))return jh(e,t,i,r,o,l,n);if(a){a=r.fallback,i=t.mode,l=e.child,o=l.sibling;var u={mode:"hidden",children:r.children};return!(i&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Lt(l,u),r.subtreeFlags=l.subtreeFlags&14680064),o!==null?a=Lt(o,a):(a=Wt(a,i,n,null),a.flags|=2),a.return=t,r.return=t,r.sibling=a,t.child=r,r=a,a=t.child,i=e.child.memoizedState,i=i===null?sa(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},a.memoizedState=i,a.childLanes=e.childLanes&~n,t.memoizedState=la,r}return a=e.child,e=a.sibling,r=Lt(a,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ei(e,t){return t=Bl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ur(e,t,n,r){return r!==null&&Ba(r),wn(t,e.child,null,n),e=ei(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function jh(e,t,n,r,l,a,i){if(n)return t.flags&256?(t.flags&=-257,r=vs(Error(F(422))),Ur(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=r.fallback,l=t.mode,r=Bl({mode:"visible",children:r.children},l,0,null),a=Wt(a,l,i,null),a.flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,t.mode&1&&wn(t,e.child,null,i),t.child.memoizedState=sa(i),t.memoizedState=la,a);if(!(t.mode&1))return Ur(e,t,i,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var o=r.dgst;return r=o,a=Error(F(419)),r=vs(a,r,void 0),Ur(e,t,i,r)}if(o=(i&e.childLanes)!==0,Ne||o){if(r=ie,r!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|i)?0:l,l!==0&&l!==a.retryLane&&(a.retryLane=l,ut(e,l),be(r,e,l,-1))}return ai(),r=vs(Error(F(421))),Ur(e,t,i,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Dh.bind(null,e),l._reactRetry=t,null):(e=a.treeContext,_e=Ct(l.nextSibling),Pe=t,K=!0,He=null,e!==null&&(Te[De++]=rt,Te[De++]=lt,Te[De++]=bt,rt=e.id,lt=e.overflow,bt=t),t=ei(t,r.children),t.flags|=4096,t)}function yo(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Zs(e.return,t,n)}function gs(e,t,n,r,l){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=l)}function Lc(e,t,n){var r=t.pendingProps,l=r.revealOrder,a=r.tail;if(ge(e,t,r.children,n),r=J.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&yo(e,n,t);else if(e.tag===19)yo(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(J,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&jl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),gs(t,!1,l,n,a);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&jl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}gs(t,!0,n,null,a);break;case"together":gs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ct(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Kt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(F(153));if(t.child!==null){for(e=t.child,n=Lt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Lt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function wh(e,t,n){switch(t.tag){case 3:_c(t),jn();break;case 5:tc(t);break;case 1:ke(t.type)&&pl(t);break;case 4:Qa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;W(gl,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(J,J.current&1),t.flags|=128,null):n&t.child.childLanes?Pc(e,t,n):(W(J,J.current&1),e=ct(e,t,n),e!==null?e.sibling:null);W(J,J.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Lc(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),W(J,J.current),r)break;return null;case 22:case 23:return t.lanes=0,Cc(e,t,n)}return ct(e,t,n)}var Fc,aa,Rc,Tc;Fc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};aa=function(){};Rc=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,At(qe.current);var a=null;switch(n){case"input":l=Ps(e,l),r=Ps(e,r),a=[];break;case"select":l=q({},l,{value:void 0}),r=q({},r,{value:void 0}),a=[];break;case"textarea":l=Rs(e,l),r=Rs(e,r),a=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=fl)}Ds(n,r);var i;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var o=l[c];for(i in o)o.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(qn.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in r){var u=r[c];if(o=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&u!==o&&(u!=null||o!=null))if(c==="style")if(o){for(i in o)!o.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in u)u.hasOwnProperty(i)&&o[i]!==u[i]&&(n||(n={}),n[i]=u[i])}else n||(a||(a=[]),a.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,o=o?o.__html:void 0,u!=null&&o!==u&&(a=a||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(a=a||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(qn.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&V("scroll",e),a||o===u||(a=[])):(a=a||[]).push(c,u))}n&&(a=a||[]).push("style",n);var c=a;(t.updateQueue=c)&&(t.flags|=4)}};Tc=function(e,t,n,r){n!==r&&(t.flags|=4)};function In(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Nh(e,t,n){var r=t.pendingProps;switch(Ua(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return ke(t.type)&&hl(),pe(t),null;case 3:return r=t.stateNode,Nn(),b(Se),b(ve),Ga(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ir(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,He!==null&&(pa(He),He=null))),aa(e,t),pe(t),null;case 5:Ka(t);var l=At(dr.current);if(n=t.type,e!==null&&t.stateNode!=null)Rc(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(F(166));return pe(t),null}if(e=At(qe.current),Ir(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[Je]=t,r[ur]=a,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(l=0;l<Wn.length;l++)V(Wn[l],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":_i(r,a),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},V("invalid",r);break;case"textarea":Li(r,a),V("invalid",r)}Ds(n,a),l=null;for(var i in a)if(a.hasOwnProperty(i)){var o=a[i];i==="children"?typeof o=="string"?r.textContent!==o&&(a.suppressHydrationWarning!==!0&&$r(r.textContent,o,e),l=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(a.suppressHydrationWarning!==!0&&$r(r.textContent,o,e),l=["children",""+o]):qn.hasOwnProperty(i)&&o!=null&&i==="onScroll"&&V("scroll",r)}switch(n){case"input":Pr(r),Pi(r,a,!0);break;case"textarea":Pr(r),Fi(r);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(r.onclick=fl)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=au(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Je]=t,e[ur]=r,Fc(e,t,!1,!1),t.stateNode=e;e:{switch(i=zs(n,r),n){case"dialog":V("cancel",e),V("close",e),l=r;break;case"iframe":case"object":case"embed":V("load",e),l=r;break;case"video":case"audio":for(l=0;l<Wn.length;l++)V(Wn[l],e);l=r;break;case"source":V("error",e),l=r;break;case"img":case"image":case"link":V("error",e),V("load",e),l=r;break;case"details":V("toggle",e),l=r;break;case"input":_i(e,r),l=Ps(e,r),V("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=q({},r,{value:void 0}),V("invalid",e);break;case"textarea":Li(e,r),l=Rs(e,r),V("invalid",e);break;default:l=r}Ds(n,l),o=l;for(a in o)if(o.hasOwnProperty(a)){var u=o[a];a==="style"?uu(e,u):a==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&iu(e,u)):a==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&er(e,u):typeof u=="number"&&er(e,""+u):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(qn.hasOwnProperty(a)?u!=null&&a==="onScroll"&&V("scroll",e):u!=null&&ka(e,a,u,i))}switch(n){case"input":Pr(e),Pi(e,r,!1);break;case"textarea":Pr(e),Fi(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ft(r.value));break;case"select":e.multiple=!!r.multiple,a=r.value,a!=null?fn(e,!!r.multiple,a,!1):r.defaultValue!=null&&fn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=fl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)Tc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(F(166));if(n=At(dr.current),At(qe.current),Ir(t)){if(r=t.stateNode,n=t.memoizedProps,r[Je]=t,(a=r.nodeValue!==n)&&(e=Pe,e!==null))switch(e.tag){case 3:$r(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&$r(r.nodeValue,n,(e.mode&1)!==0)}a&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Je]=t,t.stateNode=r}return pe(t),null;case 13:if(b(J),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(K&&_e!==null&&t.mode&1&&!(t.flags&128))Xu(),jn(),t.flags|=98560,a=!1;else if(a=Ir(t),r!==null&&r.dehydrated!==null){if(e===null){if(!a)throw Error(F(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(F(317));a[Je]=t}else jn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),a=!1}else He!==null&&(pa(He),He=null),a=!0;if(!a)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||J.current&1?se===0&&(se=3):ai())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return Nn(),aa(e,t),e===null&&ir(t.stateNode.containerInfo),pe(t),null;case 10:return Wa(t.type._context),pe(t),null;case 17:return ke(t.type)&&hl(),pe(t),null;case 19:if(b(J),a=t.memoizedState,a===null)return pe(t),null;if(r=(t.flags&128)!==0,i=a.rendering,i===null)if(r)In(a,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=jl(e),i!==null){for(t.flags|=128,In(a,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)a=n,e=r,a.flags&=14680066,i=a.alternate,i===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=i.childLanes,a.lanes=i.lanes,a.child=i.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=i.memoizedProps,a.memoizedState=i.memoizedState,a.updateQueue=i.updateQueue,a.type=i.type,e=i.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(J,J.current&1|2),t.child}e=e.sibling}a.tail!==null&&te()>kn&&(t.flags|=128,r=!0,In(a,!1),t.lanes=4194304)}else{if(!r)if(e=jl(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),In(a,!0),a.tail===null&&a.tailMode==="hidden"&&!i.alternate&&!K)return pe(t),null}else 2*te()-a.renderingStartTime>kn&&n!==1073741824&&(t.flags|=128,r=!0,In(a,!1),t.lanes=4194304);a.isBackwards?(i.sibling=t.child,t.child=i):(n=a.last,n!==null?n.sibling=i:t.child=i,a.last=i)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,n=J.current,W(J,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return si(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(F(156,t.tag))}function Sh(e,t){switch(Ua(t),t.tag){case 1:return ke(t.type)&&hl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Nn(),b(Se),b(ve),Ga(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ka(t),null;case 13:if(b(J),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(F(340));jn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return b(J),null;case 4:return Nn(),null;case 10:return Wa(t.type._context),null;case 22:case 23:return si(),null;case 24:return null;default:return null}}var Br=!1,me=!1,kh=typeof WeakSet=="function"?WeakSet:Set,M=null;function cn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(e,t,r)}else n.current=null}function ia(e,t,n){try{n()}catch(r){ee(e,t,r)}}var jo=!1;function Ch(e,t){if(Vs=ul,e=Iu(),Ia(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch{n=null;break e}var i=0,o=-1,u=-1,c=0,f=0,v=e,h=null;t:for(;;){for(var S;v!==n||l!==0&&v.nodeType!==3||(o=i+l),v!==a||r!==0&&v.nodeType!==3||(u=i+r),v.nodeType===3&&(i+=v.nodeValue.length),(S=v.firstChild)!==null;)h=v,v=S;for(;;){if(v===e)break t;if(h===n&&++c===l&&(o=i),h===a&&++f===r&&(u=i),(S=v.nextSibling)!==null)break;v=h,h=v.parentNode}v=S}n=o===-1||u===-1?null:{start:o,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(bs={focusedElem:e,selectionRange:n},ul=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var N=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(N!==null){var _=N.memoizedProps,L=N.memoizedState,p=t.stateNode,d=p.getSnapshotBeforeUpdate(t.elementType===t.type?_:Be(t.type,_),L);p.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(F(163))}}catch(w){ee(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return N=jo,jo=!1,N}function Xn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var a=l.destroy;l.destroy=void 0,a!==void 0&&ia(t,n,a)}l=l.next}while(l!==r)}}function Ol(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function oa(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Dc(e){var t=e.alternate;t!==null&&(e.alternate=null,Dc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Je],delete t[ur],delete t[Gs],delete t[ih],delete t[oh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function zc(e){return e.tag===5||e.tag===3||e.tag===4}function wo(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||zc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ua(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=fl));else if(r!==4&&(e=e.child,e!==null))for(ua(e,t,n),e=e.sibling;e!==null;)ua(e,t,n),e=e.sibling}function ca(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ca(e,t,n),e=e.sibling;e!==null;)ca(e,t,n),e=e.sibling}var ue=null,Ae=!1;function mt(e,t,n){for(n=n.child;n!==null;)Mc(e,t,n),n=n.sibling}function Mc(e,t,n){if(Ze&&typeof Ze.onCommitFiberUnmount=="function")try{Ze.onCommitFiberUnmount(Fl,n)}catch{}switch(n.tag){case 5:me||cn(n,t);case 6:var r=ue,l=Ae;ue=null,mt(e,t,n),ue=r,Ae=l,ue!==null&&(Ae?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(Ae?(e=ue,n=n.stateNode,e.nodeType===8?cs(e.parentNode,n):e.nodeType===1&&cs(e,n),lr(e)):cs(ue,n.stateNode));break;case 4:r=ue,l=Ae,ue=n.stateNode.containerInfo,Ae=!0,mt(e,t,n),ue=r,Ae=l;break;case 0:case 11:case 14:case 15:if(!me&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var a=l,i=a.destroy;a=a.tag,i!==void 0&&(a&2||a&4)&&ia(n,t,i),l=l.next}while(l!==r)}mt(e,t,n);break;case 1:if(!me&&(cn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){ee(n,t,o)}mt(e,t,n);break;case 21:mt(e,t,n);break;case 22:n.mode&1?(me=(r=me)||n.memoizedState!==null,mt(e,t,n),me=r):mt(e,t,n);break;default:mt(e,t,n)}}function No(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new kh),t.forEach(function(r){var l=zh.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Ue(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var a=e,i=t,o=i;e:for(;o!==null;){switch(o.tag){case 5:ue=o.stateNode,Ae=!1;break e;case 3:ue=o.stateNode.containerInfo,Ae=!0;break e;case 4:ue=o.stateNode.containerInfo,Ae=!0;break e}o=o.return}if(ue===null)throw Error(F(160));Mc(a,i,l),ue=null,Ae=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(c){ee(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)$c(t,e),t=t.sibling}function $c(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ue(t,e),Ye(e),r&4){try{Xn(3,e,e.return),Ol(3,e)}catch(_){ee(e,e.return,_)}try{Xn(5,e,e.return)}catch(_){ee(e,e.return,_)}}break;case 1:Ue(t,e),Ye(e),r&512&&n!==null&&cn(n,n.return);break;case 5:if(Ue(t,e),Ye(e),r&512&&n!==null&&cn(n,n.return),e.flags&32){var l=e.stateNode;try{er(l,"")}catch(_){ee(e,e.return,_)}}if(r&4&&(l=e.stateNode,l!=null)){var a=e.memoizedProps,i=n!==null?n.memoizedProps:a,o=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{o==="input"&&a.type==="radio"&&a.name!=null&&lu(l,a),zs(o,i);var c=zs(o,a);for(i=0;i<u.length;i+=2){var f=u[i],v=u[i+1];f==="style"?uu(l,v):f==="dangerouslySetInnerHTML"?iu(l,v):f==="children"?er(l,v):ka(l,f,v,c)}switch(o){case"input":Ls(l,a);break;case"textarea":su(l,a);break;case"select":var h=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!a.multiple;var S=a.value;S!=null?fn(l,!!a.multiple,S,!1):h!==!!a.multiple&&(a.defaultValue!=null?fn(l,!!a.multiple,a.defaultValue,!0):fn(l,!!a.multiple,a.multiple?[]:"",!1))}l[ur]=a}catch(_){ee(e,e.return,_)}}break;case 6:if(Ue(t,e),Ye(e),r&4){if(e.stateNode===null)throw Error(F(162));l=e.stateNode,a=e.memoizedProps;try{l.nodeValue=a}catch(_){ee(e,e.return,_)}}break;case 3:if(Ue(t,e),Ye(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{lr(t.containerInfo)}catch(_){ee(e,e.return,_)}break;case 4:Ue(t,e),Ye(e);break;case 13:Ue(t,e),Ye(e),l=e.child,l.flags&8192&&(a=l.memoizedState!==null,l.stateNode.isHidden=a,!a||l.alternate!==null&&l.alternate.memoizedState!==null||(ri=te())),r&4&&No(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(me=(c=me)||f,Ue(t,e),me=c):Ue(t,e),Ye(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(M=e,f=e.child;f!==null;){for(v=M=f;M!==null;){switch(h=M,S=h.child,h.tag){case 0:case 11:case 14:case 15:Xn(4,h,h.return);break;case 1:cn(h,h.return);var N=h.stateNode;if(typeof N.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,N.props=t.memoizedProps,N.state=t.memoizedState,N.componentWillUnmount()}catch(_){ee(r,n,_)}}break;case 5:cn(h,h.return);break;case 22:if(h.memoizedState!==null){ko(v);continue}}S!==null?(S.return=h,M=S):ko(v)}f=f.sibling}e:for(f=null,v=e;;){if(v.tag===5){if(f===null){f=v;try{l=v.stateNode,c?(a=l.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(o=v.stateNode,u=v.memoizedProps.style,i=u!=null&&u.hasOwnProperty("display")?u.display:null,o.style.display=ou("display",i))}catch(_){ee(e,e.return,_)}}}else if(v.tag===6){if(f===null)try{v.stateNode.nodeValue=c?"":v.memoizedProps}catch(_){ee(e,e.return,_)}}else if((v.tag!==22&&v.tag!==23||v.memoizedState===null||v===e)&&v.child!==null){v.child.return=v,v=v.child;continue}if(v===e)break e;for(;v.sibling===null;){if(v.return===null||v.return===e)break e;f===v&&(f=null),v=v.return}f===v&&(f=null),v.sibling.return=v.return,v=v.sibling}}break;case 19:Ue(t,e),Ye(e),r&4&&No(e);break;case 21:break;default:Ue(t,e),Ye(e)}}function Ye(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(zc(n)){var r=n;break e}n=n.return}throw Error(F(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(er(l,""),r.flags&=-33);var a=wo(e);ca(e,a,l);break;case 3:case 4:var i=r.stateNode.containerInfo,o=wo(e);ua(e,o,i);break;default:throw Error(F(161))}}catch(u){ee(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Eh(e,t,n){M=e,Ic(e)}function Ic(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var l=M,a=l.child;if(l.tag===22&&r){var i=l.memoizedState!==null||Br;if(!i){var o=l.alternate,u=o!==null&&o.memoizedState!==null||me;o=Br;var c=me;if(Br=i,(me=u)&&!c)for(M=l;M!==null;)i=M,u=i.child,i.tag===22&&i.memoizedState!==null?Co(l):u!==null?(u.return=i,M=u):Co(l);for(;a!==null;)M=a,Ic(a),a=a.sibling;M=l,Br=o,me=c}So(e)}else l.subtreeFlags&8772&&a!==null?(a.return=l,M=a):So(e)}}function So(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:me||Ol(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!me)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Be(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&ao(t,a,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ao(t,i,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var v=f.dehydrated;v!==null&&lr(v)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(F(163))}me||t.flags&512&&oa(t)}catch(h){ee(t,t.return,h)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function ko(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Co(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ol(4,t)}catch(u){ee(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){ee(t,l,u)}}var a=t.return;try{oa(t)}catch(u){ee(t,a,u)}break;case 5:var i=t.return;try{oa(t)}catch(u){ee(t,i,u)}}}catch(u){ee(t,t.return,u)}if(t===e){M=null;break}var o=t.sibling;if(o!==null){o.return=t.return,M=o;break}M=t.return}}var _h=Math.ceil,Sl=ft.ReactCurrentDispatcher,ti=ft.ReactCurrentOwner,$e=ft.ReactCurrentBatchConfig,A=0,ie=null,re=null,ce=0,Ee=0,dn=Dt(0),se=0,mr=null,Kt=0,Ul=0,ni=0,Jn=null,we=null,ri=0,kn=1/0,tt=null,kl=!1,da=null,_t=null,Ar=!1,wt=null,Cl=0,Zn=0,fa=null,qr=-1,el=0;function xe(){return A&6?te():qr!==-1?qr:qr=te()}function Pt(e){return e.mode&1?A&2&&ce!==0?ce&-ce:ch.transition!==null?(el===0&&(el=wu()),el):(e=H,e!==0||(e=window.event,e=e===void 0?16:Pu(e.type)),e):1}function be(e,t,n,r){if(50<Zn)throw Zn=0,fa=null,Error(F(185));xr(e,n,r),(!(A&2)||e!==ie)&&(e===ie&&(!(A&2)&&(Ul|=n),se===4&&yt(e,ce)),Ce(e,r),n===1&&A===0&&!(t.mode&1)&&(kn=te()+500,Ml&&zt()))}function Ce(e,t){var n=e.callbackNode;cf(e,t);var r=ol(e,e===ie?ce:0);if(r===0)n!==null&&Di(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Di(n),t===1)e.tag===0?uh(Eo.bind(null,e)):Ku(Eo.bind(null,e)),sh(function(){!(A&6)&&zt()}),n=null;else{switch(Nu(r)){case 1:n=La;break;case 4:n=yu;break;case 16:n=il;break;case 536870912:n=ju;break;default:n=il}n=bc(n,Oc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Oc(e,t){if(qr=-1,el=0,A&6)throw Error(F(327));var n=e.callbackNode;if(gn()&&e.callbackNode!==n)return null;var r=ol(e,e===ie?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=El(e,r);else{t=r;var l=A;A|=2;var a=Bc();(ie!==e||ce!==t)&&(tt=null,kn=te()+500,Ht(e,t));do try{Fh();break}catch(o){Uc(e,o)}while(!0);Ha(),Sl.current=a,A=l,re!==null?t=0:(ie=null,ce=0,t=se)}if(t!==0){if(t===2&&(l=Us(e),l!==0&&(r=l,t=ha(e,l))),t===1)throw n=mr,Ht(e,0),yt(e,r),Ce(e,te()),n;if(t===6)yt(e,r);else{if(l=e.current.alternate,!(r&30)&&!Ph(l)&&(t=El(e,r),t===2&&(a=Us(e),a!==0&&(r=a,t=ha(e,a))),t===1))throw n=mr,Ht(e,0),yt(e,r),Ce(e,te()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(F(345));case 2:It(e,we,tt);break;case 3:if(yt(e,r),(r&130023424)===r&&(t=ri+500-te(),10<t)){if(ol(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Ks(It.bind(null,e,we,tt),t);break}It(e,we,tt);break;case 4:if(yt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var i=31-Ve(r);a=1<<i,i=t[i],i>l&&(l=i),r&=~a}if(r=l,r=te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*_h(r/1960))-r,10<r){e.timeoutHandle=Ks(It.bind(null,e,we,tt),r);break}It(e,we,tt);break;case 5:It(e,we,tt);break;default:throw Error(F(329))}}}return Ce(e,te()),e.callbackNode===n?Oc.bind(null,e):null}function ha(e,t){var n=Jn;return e.current.memoizedState.isDehydrated&&(Ht(e,t).flags|=256),e=El(e,t),e!==2&&(t=we,we=n,t!==null&&pa(t)),e}function pa(e){we===null?we=e:we.push.apply(we,e)}function Ph(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],a=l.getSnapshot;l=l.value;try{if(!Qe(a(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function yt(e,t){for(t&=~ni,t&=~Ul,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ve(t),r=1<<n;e[n]=-1,t&=~r}}function Eo(e){if(A&6)throw Error(F(327));gn();var t=ol(e,0);if(!(t&1))return Ce(e,te()),null;var n=El(e,t);if(e.tag!==0&&n===2){var r=Us(e);r!==0&&(t=r,n=ha(e,r))}if(n===1)throw n=mr,Ht(e,0),yt(e,t),Ce(e,te()),n;if(n===6)throw Error(F(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,It(e,we,tt),Ce(e,te()),null}function li(e,t){var n=A;A|=1;try{return e(t)}finally{A=n,A===0&&(kn=te()+500,Ml&&zt())}}function Gt(e){wt!==null&&wt.tag===0&&!(A&6)&&gn();var t=A;A|=1;var n=$e.transition,r=H;try{if($e.transition=null,H=1,e)return e()}finally{H=r,$e.transition=n,A=t,!(A&6)&&zt()}}function si(){Ee=dn.current,b(dn)}function Ht(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,lh(n)),re!==null)for(n=re.return;n!==null;){var r=n;switch(Ua(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&hl();break;case 3:Nn(),b(Se),b(ve),Ga();break;case 5:Ka(r);break;case 4:Nn();break;case 13:b(J);break;case 19:b(J);break;case 10:Wa(r.type._context);break;case 22:case 23:si()}n=n.return}if(ie=e,re=e=Lt(e.current,null),ce=Ee=t,se=0,mr=null,ni=Ul=Kt=0,we=Jn=null,Bt!==null){for(t=0;t<Bt.length;t++)if(n=Bt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,a=n.pending;if(a!==null){var i=a.next;a.next=l,r.next=i}n.pending=r}Bt=null}return e}function Uc(e,t){do{var n=re;try{if(Ha(),Xr.current=Nl,wl){for(var r=Z.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}wl=!1}if(Qt=0,ae=le=Z=null,Yn=!1,fr=0,ti.current=null,n===null||n.return===null){se=1,mr=t,re=null;break}e:{var a=e,i=n.return,o=n,u=t;if(t=ce,o.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,f=o,v=f.tag;if(!(f.mode&1)&&(v===0||v===11||v===15)){var h=f.alternate;h?(f.updateQueue=h.updateQueue,f.memoizedState=h.memoizedState,f.lanes=h.lanes):(f.updateQueue=null,f.memoizedState=null)}var S=ho(i);if(S!==null){S.flags&=-257,po(S,i,o,a,t),S.mode&1&&fo(a,c,t),t=S,u=c;var N=t.updateQueue;if(N===null){var _=new Set;_.add(u),t.updateQueue=_}else N.add(u);break e}else{if(!(t&1)){fo(a,c,t),ai();break e}u=Error(F(426))}}else if(K&&o.mode&1){var L=ho(i);if(L!==null){!(L.flags&65536)&&(L.flags|=256),po(L,i,o,a,t),Ba(Sn(u,o));break e}}a=u=Sn(u,o),se!==4&&(se=2),Jn===null?Jn=[a]:Jn.push(a),a=i;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var p=Nc(a,u,t);so(a,p);break e;case 1:o=u;var d=a.type,m=a.stateNode;if(!(a.flags&128)&&(typeof d.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(_t===null||!_t.has(m)))){a.flags|=65536,t&=-t,a.lanes|=t;var w=Sc(a,o,t);so(a,w);break e}}a=a.return}while(a!==null)}Hc(n)}catch(x){t=x,re===n&&n!==null&&(re=n=n.return);continue}break}while(!0)}function Bc(){var e=Sl.current;return Sl.current=Nl,e===null?Nl:e}function ai(){(se===0||se===3||se===2)&&(se=4),ie===null||!(Kt&268435455)&&!(Ul&268435455)||yt(ie,ce)}function El(e,t){var n=A;A|=2;var r=Bc();(ie!==e||ce!==t)&&(tt=null,Ht(e,t));do try{Lh();break}catch(l){Uc(e,l)}while(!0);if(Ha(),A=n,Sl.current=r,re!==null)throw Error(F(261));return ie=null,ce=0,se}function Lh(){for(;re!==null;)Ac(re)}function Fh(){for(;re!==null&&!ef();)Ac(re)}function Ac(e){var t=Vc(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?Hc(e):re=t,ti.current=null}function Hc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Sh(n,t),n!==null){n.flags&=32767,re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,re=null;return}}else if(n=Nh(n,t,Ee),n!==null){re=n;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);se===0&&(se=5)}function It(e,t,n){var r=H,l=$e.transition;try{$e.transition=null,H=1,Rh(e,t,n,r)}finally{$e.transition=l,H=r}return null}function Rh(e,t,n,r){do gn();while(wt!==null);if(A&6)throw Error(F(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(F(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(df(e,a),e===ie&&(re=ie=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ar||(Ar=!0,bc(il,function(){return gn(),null})),a=(n.flags&15990)!==0,n.subtreeFlags&15990||a){a=$e.transition,$e.transition=null;var i=H;H=1;var o=A;A|=4,ti.current=null,Ch(e,n),$c(n,e),Jf(bs),ul=!!Vs,bs=Vs=null,e.current=n,Eh(n),tf(),A=o,H=i,$e.transition=a}else e.current=n;if(Ar&&(Ar=!1,wt=e,Cl=l),a=e.pendingLanes,a===0&&(_t=null),lf(n.stateNode),Ce(e,te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(kl)throw kl=!1,e=da,da=null,e;return Cl&1&&e.tag!==0&&gn(),a=e.pendingLanes,a&1?e===fa?Zn++:(Zn=0,fa=e):Zn=0,zt(),null}function gn(){if(wt!==null){var e=Nu(Cl),t=$e.transition,n=H;try{if($e.transition=null,H=16>e?16:e,wt===null)var r=!1;else{if(e=wt,wt=null,Cl=0,A&6)throw Error(F(331));var l=A;for(A|=4,M=e.current;M!==null;){var a=M,i=a.child;if(M.flags&16){var o=a.deletions;if(o!==null){for(var u=0;u<o.length;u++){var c=o[u];for(M=c;M!==null;){var f=M;switch(f.tag){case 0:case 11:case 15:Xn(8,f,a)}var v=f.child;if(v!==null)v.return=f,M=v;else for(;M!==null;){f=M;var h=f.sibling,S=f.return;if(Dc(f),f===c){M=null;break}if(h!==null){h.return=S,M=h;break}M=S}}}var N=a.alternate;if(N!==null){var _=N.child;if(_!==null){N.child=null;do{var L=_.sibling;_.sibling=null,_=L}while(_!==null)}}M=a}}if(a.subtreeFlags&2064&&i!==null)i.return=a,M=i;else e:for(;M!==null;){if(a=M,a.flags&2048)switch(a.tag){case 0:case 11:case 15:Xn(9,a,a.return)}var p=a.sibling;if(p!==null){p.return=a.return,M=p;break e}M=a.return}}var d=e.current;for(M=d;M!==null;){i=M;var m=i.child;if(i.subtreeFlags&2064&&m!==null)m.return=i,M=m;else e:for(i=d;M!==null;){if(o=M,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Ol(9,o)}}catch(x){ee(o,o.return,x)}if(o===i){M=null;break e}var w=o.sibling;if(w!==null){w.return=o.return,M=w;break e}M=o.return}}if(A=l,zt(),Ze&&typeof Ze.onPostCommitFiberRoot=="function")try{Ze.onPostCommitFiberRoot(Fl,e)}catch{}r=!0}return r}finally{H=n,$e.transition=t}}return!1}function _o(e,t,n){t=Sn(n,t),t=Nc(e,t,1),e=Et(e,t,1),t=xe(),e!==null&&(xr(e,1,t),Ce(e,t))}function ee(e,t,n){if(e.tag===3)_o(e,e,n);else for(;t!==null;){if(t.tag===3){_o(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_t===null||!_t.has(r))){e=Sn(n,e),e=Sc(t,e,1),t=Et(t,e,1),e=xe(),t!==null&&(xr(t,1,e),Ce(t,e));break}}t=t.return}}function Th(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,ie===e&&(ce&n)===n&&(se===4||se===3&&(ce&130023424)===ce&&500>te()-ri?Ht(e,0):ni|=n),Ce(e,t)}function Wc(e,t){t===0&&(e.mode&1?(t=Rr,Rr<<=1,!(Rr&130023424)&&(Rr=4194304)):t=1);var n=xe();e=ut(e,t),e!==null&&(xr(e,t,n),Ce(e,n))}function Dh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Wc(e,n)}function zh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(F(314))}r!==null&&r.delete(t),Wc(e,n)}var Vc;Vc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Se.current)Ne=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ne=!1,wh(e,t,n);Ne=!!(e.flags&131072)}else Ne=!1,K&&t.flags&1048576&&Gu(t,vl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Zr(e,t),e=t.pendingProps;var l=yn(t,ve.current);vn(t,n),l=Xa(null,t,r,e,l,n);var a=Ja();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ke(r)?(a=!0,pl(t)):a=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,ba(t),l.updater=Il,t.stateNode=l,l._reactInternals=t,ea(t,r,e,n),t=ra(null,t,r,!0,a,n)):(t.tag=0,K&&a&&Oa(t),ge(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Zr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=$h(r),e=Be(r,e),l){case 0:t=na(null,t,r,e,n);break e;case 1:t=go(null,t,r,e,n);break e;case 11:t=mo(null,t,r,e,n);break e;case 14:t=vo(null,t,r,Be(r.type,e),n);break e}throw Error(F(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Be(r,l),na(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Be(r,l),go(e,t,r,l,n);case 3:e:{if(_c(t),e===null)throw Error(F(387));r=t.pendingProps,a=t.memoizedState,l=a.element,ec(e,t),yl(t,r,null,n);var i=t.memoizedState;if(r=i.element,a.isDehydrated)if(a={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){l=Sn(Error(F(423)),t),t=xo(e,t,r,n,l);break e}else if(r!==l){l=Sn(Error(F(424)),t),t=xo(e,t,r,n,l);break e}else for(_e=Ct(t.stateNode.containerInfo.firstChild),Pe=t,K=!0,He=null,n=Zu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(jn(),r===l){t=ct(e,t,n);break e}ge(e,t,r,n)}t=t.child}return t;case 5:return tc(t),e===null&&Js(t),r=t.type,l=t.pendingProps,a=e!==null?e.memoizedProps:null,i=l.children,Qs(r,l)?i=null:a!==null&&Qs(r,a)&&(t.flags|=32),Ec(e,t),ge(e,t,i,n),t.child;case 6:return e===null&&Js(t),null;case 13:return Pc(e,t,n);case 4:return Qa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=wn(t,null,r,n):ge(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Be(r,l),mo(e,t,r,l,n);case 7:return ge(e,t,t.pendingProps,n),t.child;case 8:return ge(e,t,t.pendingProps.children,n),t.child;case 12:return ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,a=t.memoizedProps,i=l.value,W(gl,r._currentValue),r._currentValue=i,a!==null)if(Qe(a.value,i)){if(a.children===l.children&&!Se.current){t=ct(e,t,n);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var o=a.dependencies;if(o!==null){i=a.child;for(var u=o.firstContext;u!==null;){if(u.context===r){if(a.tag===1){u=st(-1,n&-n),u.tag=2;var c=a.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?u.next=u:(u.next=f.next,f.next=u),c.pending=u}}a.lanes|=n,u=a.alternate,u!==null&&(u.lanes|=n),Zs(a.return,n,t),o.lanes|=n;break}u=u.next}}else if(a.tag===10)i=a.type===t.type?null:a.child;else if(a.tag===18){if(i=a.return,i===null)throw Error(F(341));i.lanes|=n,o=i.alternate,o!==null&&(o.lanes|=n),Zs(i,n,t),i=a.sibling}else i=a.child;if(i!==null)i.return=a;else for(i=a;i!==null;){if(i===t){i=null;break}if(a=i.sibling,a!==null){a.return=i.return,i=a;break}i=i.return}a=i}ge(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,vn(t,n),l=Ie(l),r=r(l),t.flags|=1,ge(e,t,r,n),t.child;case 14:return r=t.type,l=Be(r,t.pendingProps),l=Be(r.type,l),vo(e,t,r,l,n);case 15:return kc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Be(r,l),Zr(e,t),t.tag=1,ke(r)?(e=!0,pl(t)):e=!1,vn(t,n),wc(t,r,l),ea(t,r,l,n),ra(null,t,r,!0,e,n);case 19:return Lc(e,t,n);case 22:return Cc(e,t,n)}throw Error(F(156,t.tag))};function bc(e,t){return xu(e,t)}function Mh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new Mh(e,t,n,r)}function ii(e){return e=e.prototype,!(!e||!e.isReactComponent)}function $h(e){if(typeof e=="function")return ii(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ea)return 11;if(e===_a)return 14}return 2}function Lt(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function tl(e,t,n,r,l,a){var i=2;if(r=e,typeof e=="function")ii(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case en:return Wt(n.children,l,a,t);case Ca:i=8,l|=8;break;case ks:return e=ze(12,n,t,l|2),e.elementType=ks,e.lanes=a,e;case Cs:return e=ze(13,n,t,l),e.elementType=Cs,e.lanes=a,e;case Es:return e=ze(19,n,t,l),e.elementType=Es,e.lanes=a,e;case tu:return Bl(n,l,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case qo:i=10;break e;case eu:i=9;break e;case Ea:i=11;break e;case _a:i=14;break e;case vt:i=16,r=null;break e}throw Error(F(130,e==null?e:typeof e,""))}return t=ze(i,n,t,l),t.elementType=e,t.type=r,t.lanes=a,t}function Wt(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Bl(e,t,n,r){return e=ze(22,e,r,t),e.elementType=tu,e.lanes=n,e.stateNode={isHidden:!1},e}function xs(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function ys(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ih(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ql(0),this.expirationTimes=ql(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ql(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function oi(e,t,n,r,l,a,i,o,u){return e=new Ih(e,t,n,o,u),t===1?(t=1,a===!0&&(t|=8)):t=0,a=ze(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ba(a),e}function Oh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:qt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Qc(e){if(!e)return Rt;e=e._reactInternals;e:{if(Jt(e)!==e||e.tag!==1)throw Error(F(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ke(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(F(171))}if(e.tag===1){var n=e.type;if(ke(n))return Qu(e,n,t)}return t}function Kc(e,t,n,r,l,a,i,o,u){return e=oi(n,r,!0,e,l,a,i,o,u),e.context=Qc(null),n=e.current,r=xe(),l=Pt(n),a=st(r,l),a.callback=t??null,Et(n,a,l),e.current.lanes=l,xr(e,l,r),Ce(e,r),e}function Al(e,t,n,r){var l=t.current,a=xe(),i=Pt(l);return n=Qc(n),t.context===null?t.context=n:t.pendingContext=n,t=st(a,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Et(l,t,i),e!==null&&(be(e,l,i,a),Yr(e,l,i)),i}function _l(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Po(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ui(e,t){Po(e,t),(e=e.alternate)&&Po(e,t)}function Uh(){return null}var Gc=typeof reportError=="function"?reportError:function(e){console.error(e)};function ci(e){this._internalRoot=e}Hl.prototype.render=ci.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(F(409));Al(e,t,null,null)};Hl.prototype.unmount=ci.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Gt(function(){Al(null,e,null,null)}),t[ot]=null}};function Hl(e){this._internalRoot=e}Hl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Cu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<xt.length&&t!==0&&t<xt[n].priority;n++);xt.splice(n,0,e),n===0&&_u(e)}};function di(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Wl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Lo(){}function Bh(e,t,n,r,l){if(l){if(typeof r=="function"){var a=r;r=function(){var c=_l(i);a.call(c)}}var i=Kc(t,r,e,0,null,!1,!1,"",Lo);return e._reactRootContainer=i,e[ot]=i.current,ir(e.nodeType===8?e.parentNode:e),Gt(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var o=r;r=function(){var c=_l(u);o.call(c)}}var u=oi(e,0,!1,null,null,!1,!1,"",Lo);return e._reactRootContainer=u,e[ot]=u.current,ir(e.nodeType===8?e.parentNode:e),Gt(function(){Al(t,u,n,r)}),u}function Vl(e,t,n,r,l){var a=n._reactRootContainer;if(a){var i=a;if(typeof l=="function"){var o=l;l=function(){var u=_l(i);o.call(u)}}Al(t,i,e,l)}else i=Bh(n,t,e,l,r);return _l(i)}Su=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Hn(t.pendingLanes);n!==0&&(Fa(t,n|1),Ce(t,te()),!(A&6)&&(kn=te()+500,zt()))}break;case 13:Gt(function(){var r=ut(e,1);if(r!==null){var l=xe();be(r,e,1,l)}}),ui(e,1)}};Ra=function(e){if(e.tag===13){var t=ut(e,134217728);if(t!==null){var n=xe();be(t,e,134217728,n)}ui(e,134217728)}};ku=function(e){if(e.tag===13){var t=Pt(e),n=ut(e,t);if(n!==null){var r=xe();be(n,e,t,r)}ui(e,t)}};Cu=function(){return H};Eu=function(e,t){var n=H;try{return H=e,t()}finally{H=n}};$s=function(e,t,n){switch(t){case"input":if(Ls(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=zl(r);if(!l)throw Error(F(90));ru(r),Ls(r,l)}}}break;case"textarea":su(e,n);break;case"select":t=n.value,t!=null&&fn(e,!!n.multiple,t,!1)}};fu=li;hu=Gt;var Ah={usingClientEntryPoint:!1,Events:[jr,ln,zl,cu,du,li]},On={findFiberByHostInstance:Ut,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Hh={bundleType:On.bundleType,version:On.version,rendererPackageName:On.rendererPackageName,rendererConfig:On.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ft.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=vu(e),e===null?null:e.stateNode},findFiberByHostInstance:On.findFiberByHostInstance||Uh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Hr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Hr.isDisabled&&Hr.supportsFiber)try{Fl=Hr.inject(Hh),Ze=Hr}catch{}}Fe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ah;Fe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!di(t))throw Error(F(200));return Oh(e,t,null,n)};Fe.createRoot=function(e,t){if(!di(e))throw Error(F(299));var n=!1,r="",l=Gc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=oi(e,1,!1,null,null,n,!1,r,l),e[ot]=t.current,ir(e.nodeType===8?e.parentNode:e),new ci(t)};Fe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(F(188)):(e=Object.keys(e).join(","),Error(F(268,e)));return e=vu(t),e=e===null?null:e.stateNode,e};Fe.flushSync=function(e){return Gt(e)};Fe.hydrate=function(e,t,n){if(!Wl(t))throw Error(F(200));return Vl(null,e,t,!0,n)};Fe.hydrateRoot=function(e,t,n){if(!di(e))throw Error(F(405));var r=n!=null&&n.hydratedSources||null,l=!1,a="",i=Gc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Kc(t,null,e,1,n??null,l,!1,a,i),e[ot]=t.current,ir(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Hl(t)};Fe.render=function(e,t,n){if(!Wl(t))throw Error(F(200));return Vl(null,e,t,!1,n)};Fe.unmountComponentAtNode=function(e){if(!Wl(e))throw Error(F(40));return e._reactRootContainer?(Gt(function(){Vl(null,null,e,!1,function(){e._reactRootContainer=null,e[ot]=null})}),!0):!1};Fe.unstable_batchedUpdates=li;Fe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Wl(n))throw Error(F(200));if(e==null||e._reactInternals===void 0)throw Error(F(38));return Vl(e,t,n,!1,r)};Fe.version="18.3.1-next-f1338f8080-20240426";function Yc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Yc)}catch(e){console.error(e)}}Yc(),Yo.exports=Fe;var Wh=Yo.exports,Fo=Wh;Ns.createRoot=Fo.createRoot,Ns.hydrateRoot=Fo.hydrateRoot;var fi={};Object.defineProperty(fi,"__esModule",{value:!0});fi.parse=Xh;fi.serialize=Jh;const Vh=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,bh=/^[\u0021-\u003A\u003C-\u007E]*$/,Qh=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Kh=/^[\u0020-\u003A\u003D-\u007E]*$/,Gh=Object.prototype.toString,Yh=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Xh(e,t){const n=new Yh,r=e.length;if(r<2)return n;const l=(t==null?void 0:t.decode)||Zh;let a=0;do{const i=e.indexOf("=",a);if(i===-1)break;const o=e.indexOf(";",a),u=o===-1?r:o;if(i>u){a=e.lastIndexOf(";",i-1)+1;continue}const c=Ro(e,a,i),f=To(e,i,c),v=e.slice(c,f);if(n[v]===void 0){let h=Ro(e,i+1,u),S=To(e,u,h);const N=l(e.slice(h,S));n[v]=N}a=u+1}while(a<r);return n}function Ro(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function To(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function Jh(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!Vh.test(e))throw new TypeError(`argument name is invalid: ${e}`);const l=r(t);if(!bh.test(l))throw new TypeError(`argument val is invalid: ${t}`);let a=e+"="+l;if(!n)return a;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);a+="; Max-Age="+n.maxAge}if(n.domain){if(!Qh.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);a+="; Domain="+n.domain}if(n.path){if(!Kh.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);a+="; Path="+n.path}if(n.expires){if(!qh(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);a+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(a+="; HttpOnly"),n.secure&&(a+="; Secure"),n.partitioned&&(a+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return a}function Zh(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function qh(e){return Gh.call(e)==="[object Date]"}var Do="popstate";function ep(e={}){function t(r,l){let{pathname:a,search:i,hash:o}=r.location;return ma("",{pathname:a,search:i,hash:o},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function n(r,l){return typeof l=="string"?l:vr(l)}return np(t,n,null,e)}function G(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ke(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function tp(){return Math.random().toString(36).substring(2,10)}function zo(e,t){return{usr:e.state,key:e.key,idx:t}}function ma(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Pn(t):t,state:n,key:t&&t.key||r||tp()}}function vr({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function Pn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function np(e,t,n,r={}){let{window:l=document.defaultView,v5Compat:a=!1}=r,i=l.history,o="POP",u=null,c=f();c==null&&(c=0,i.replaceState({...i.state,idx:c},""));function f(){return(i.state||{idx:null}).idx}function v(){o="POP";let L=f(),p=L==null?null:L-c;c=L,u&&u({action:o,location:_.location,delta:p})}function h(L,p){o="PUSH";let d=ma(_.location,L,p);c=f()+1;let m=zo(d,c),w=_.createHref(d);try{i.pushState(m,"",w)}catch(x){if(x instanceof DOMException&&x.name==="DataCloneError")throw x;l.location.assign(w)}a&&u&&u({action:o,location:_.location,delta:1})}function S(L,p){o="REPLACE";let d=ma(_.location,L,p);c=f();let m=zo(d,c),w=_.createHref(d);i.replaceState(m,"",w),a&&u&&u({action:o,location:_.location,delta:0})}function N(L){return rp(L)}let _={get action(){return o},get location(){return e(l,i)},listen(L){if(u)throw new Error("A history only accepts one active listener");return l.addEventListener(Do,v),u=L,()=>{l.removeEventListener(Do,v),u=null}},createHref(L){return t(l,L)},createURL:N,encodeLocation(L){let p=N(L);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:h,replace:S,go(L){return i.go(L)}};return _}function rp(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),G(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:vr(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function Xc(e,t,n="/"){return lp(e,t,n,!1)}function lp(e,t,n,r){let l=typeof t=="string"?Pn(t):t,a=dt(l.pathname||"/",n);if(a==null)return null;let i=Jc(e);sp(i);let o=null;for(let u=0;o==null&&u<i.length;++u){let c=vp(a);o=pp(i[u],c,r)}return o}function Jc(e,t=[],n=[],r=""){let l=(a,i,o)=>{let u={relativePath:o===void 0?a.path||"":o,caseSensitive:a.caseSensitive===!0,childrenIndex:i,route:a};u.relativePath.startsWith("/")&&(G(u.relativePath.startsWith(r),`Absolute route path "${u.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),u.relativePath=u.relativePath.slice(r.length));let c=at([r,u.relativePath]),f=n.concat(u);a.children&&a.children.length>0&&(G(a.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),Jc(a.children,t,f,c)),!(a.path==null&&!a.index)&&t.push({path:c,score:fp(c,a.index),routesMeta:f})};return e.forEach((a,i)=>{var o;if(a.path===""||!((o=a.path)!=null&&o.includes("?")))l(a,i);else for(let u of Zc(a.path))l(a,i,u)}),t}function Zc(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,l=n.endsWith("?"),a=n.replace(/\?$/,"");if(r.length===0)return l?[a,""]:[a];let i=Zc(r.join("/")),o=[];return o.push(...i.map(u=>u===""?a:[a,u].join("/"))),l&&o.push(...i),o.map(u=>e.startsWith("/")&&u===""?"/":u)}function sp(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:hp(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var ap=/^:[\w-]+$/,ip=3,op=2,up=1,cp=10,dp=-2,Mo=e=>e==="*";function fp(e,t){let n=e.split("/"),r=n.length;return n.some(Mo)&&(r+=dp),t&&(r+=op),n.filter(l=>!Mo(l)).reduce((l,a)=>l+(ap.test(a)?ip:a===""?up:cp),r)}function hp(e,t){return e.length===t.length&&e.slice(0,-1).every((r,l)=>r===t[l])?e[e.length-1]-t[t.length-1]:0}function pp(e,t,n=!1){let{routesMeta:r}=e,l={},a="/",i=[];for(let o=0;o<r.length;++o){let u=r[o],c=o===r.length-1,f=a==="/"?t:t.slice(a.length)||"/",v=Pl({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},f),h=u.route;if(!v&&c&&n&&!r[r.length-1].route.index&&(v=Pl({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},f)),!v)return null;Object.assign(l,v.params),i.push({params:l,pathname:at([a,v.pathname]),pathnameBase:jp(at([a,v.pathnameBase])),route:h}),v.pathnameBase!=="/"&&(a=at([a,v.pathnameBase]))}return i}function Pl(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=mp(e.path,e.caseSensitive,e.end),l=t.match(n);if(!l)return null;let a=l[0],i=a.replace(/(.)\/+$/,"$1"),o=l.slice(1);return{params:r.reduce((c,{paramName:f,isOptional:v},h)=>{if(f==="*"){let N=o[h]||"";i=a.slice(0,a.length-N.length).replace(/(.)\/+$/,"$1")}const S=o[h];return v&&!S?c[f]=void 0:c[f]=(S||"").replace(/%2F/g,"/"),c},{}),pathname:a,pathnameBase:i,pattern:e}}function mp(e,t=!1,n=!0){Ke(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,o,u)=>(r.push({paramName:o,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),r]}function vp(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ke(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function dt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function gp(e,t="/"){let{pathname:n,search:r="",hash:l=""}=typeof e=="string"?Pn(e):e;return{pathname:n?n.startsWith("/")?n:xp(n,t):t,search:wp(r),hash:Np(l)}}function xp(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?n.length>1&&n.pop():l!=="."&&n.push(l)}),n.length>1?n.join("/"):"/"}function js(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function yp(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function hi(e){let t=yp(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function pi(e,t,n,r=!1){let l;typeof e=="string"?l=Pn(e):(l={...e},G(!l.pathname||!l.pathname.includes("?"),js("?","pathname","search",l)),G(!l.pathname||!l.pathname.includes("#"),js("#","pathname","hash",l)),G(!l.search||!l.search.includes("#"),js("#","search","hash",l)));let a=e===""||l.pathname==="",i=a?"/":l.pathname,o;if(i==null)o=n;else{let v=t.length-1;if(!r&&i.startsWith("..")){let h=i.split("/");for(;h[0]==="..";)h.shift(),v-=1;l.pathname=h.join("/")}o=v>=0?t[v]:"/"}let u=gp(l,o),c=i&&i!=="/"&&i.endsWith("/"),f=(a||i===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||f)&&(u.pathname+="/"),u}var at=e=>e.join("/").replace(/\/\/+/g,"/"),jp=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),wp=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Np=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Sp(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var qc=["POST","PUT","PATCH","DELETE"];new Set(qc);var kp=["GET",...qc];new Set(kp);var Ln=g.createContext(null);Ln.displayName="DataRouter";var bl=g.createContext(null);bl.displayName="DataRouterState";var ed=g.createContext({isTransitioning:!1});ed.displayName="ViewTransition";var Cp=g.createContext(new Map);Cp.displayName="Fetchers";var Ep=g.createContext(null);Ep.displayName="Await";var Ge=g.createContext(null);Ge.displayName="Navigation";var Nr=g.createContext(null);Nr.displayName="Location";var et=g.createContext({outlet:null,matches:[],isDataRoute:!1});et.displayName="Route";var mi=g.createContext(null);mi.displayName="RouteError";function _p(e,{relative:t}={}){G(Fn(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=g.useContext(Ge),{hash:l,pathname:a,search:i}=Sr(e,{relative:t}),o=a;return n!=="/"&&(o=a==="/"?n:at([n,a])),r.createHref({pathname:o,search:i,hash:l})}function Fn(){return g.useContext(Nr)!=null}function ht(){return G(Fn(),"useLocation() may be used only in the context of a <Router> component."),g.useContext(Nr).location}var td="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function nd(e){g.useContext(Ge).static||g.useLayoutEffect(e)}function rd(){let{isDataRoute:e}=g.useContext(et);return e?Bp():Pp()}function Pp(){G(Fn(),"useNavigate() may be used only in the context of a <Router> component.");let e=g.useContext(Ln),{basename:t,navigator:n}=g.useContext(Ge),{matches:r}=g.useContext(et),{pathname:l}=ht(),a=JSON.stringify(hi(r)),i=g.useRef(!1);return nd(()=>{i.current=!0}),g.useCallback((u,c={})=>{if(Ke(i.current,td),!i.current)return;if(typeof u=="number"){n.go(u);return}let f=pi(u,JSON.parse(a),l,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:at([t,f.pathname])),(c.replace?n.replace:n.push)(f,c.state,c)},[t,n,a,l,e])}g.createContext(null);function Sr(e,{relative:t}={}){let{matches:n}=g.useContext(et),{pathname:r}=ht(),l=JSON.stringify(hi(n));return g.useMemo(()=>pi(e,JSON.parse(l),r,t==="path"),[e,l,r,t])}function Lp(e,t){return ld(e,t)}function ld(e,t,n,r){var p;G(Fn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=g.useContext(Ge),{matches:a}=g.useContext(et),i=a[a.length-1],o=i?i.params:{},u=i?i.pathname:"/",c=i?i.pathnameBase:"/",f=i&&i.route;{let d=f&&f.path||"";sd(u,!f||d.endsWith("*")||d.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${d}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${d}"> to <Route path="${d==="/"?"*":`${d}/*`}">.`)}let v=ht(),h;if(t){let d=typeof t=="string"?Pn(t):t;G(c==="/"||((p=d.pathname)==null?void 0:p.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${d.pathname}" was given in the \`location\` prop.`),h=d}else h=v;let S=h.pathname||"/",N=S;if(c!=="/"){let d=c.replace(/^\//,"").split("/");N="/"+S.replace(/^\//,"").split("/").slice(d.length).join("/")}let _=Xc(e,{pathname:N});Ke(f||_!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),Ke(_==null||_[_.length-1].route.element!==void 0||_[_.length-1].route.Component!==void 0||_[_.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let L=zp(_&&_.map(d=>Object.assign({},d,{params:Object.assign({},o,d.params),pathname:at([c,l.encodeLocation?l.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?c:at([c,l.encodeLocation?l.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),a,n,r);return t&&L?g.createElement(Nr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...h},navigationType:"POP"}},L):L}function Fp(){let e=Up(),t=Sp(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:r},a={padding:"2px 4px",backgroundColor:r},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=g.createElement(g.Fragment,null,g.createElement("p",null,"💿 Hey developer 👋"),g.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",g.createElement("code",{style:a},"ErrorBoundary")," or"," ",g.createElement("code",{style:a},"errorElement")," prop on your route.")),g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),n?g.createElement("pre",{style:l},n):null,i)}var Rp=g.createElement(Fp,null),Tp=class extends g.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?g.createElement(et.Provider,{value:this.props.routeContext},g.createElement(mi.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Dp({routeContext:e,match:t,children:n}){let r=g.useContext(Ln);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),g.createElement(et.Provider,{value:e},n)}function zp(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,a=n==null?void 0:n.errors;if(a!=null){let u=l.findIndex(c=>c.route.id&&(a==null?void 0:a[c.route.id])!==void 0);G(u>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(a).join(",")}`),l=l.slice(0,Math.min(l.length,u+1))}let i=!1,o=-1;if(n)for(let u=0;u<l.length;u++){let c=l[u];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(o=u),c.route.id){let{loaderData:f,errors:v}=n,h=c.route.loader&&!f.hasOwnProperty(c.route.id)&&(!v||v[c.route.id]===void 0);if(c.route.lazy||h){i=!0,o>=0?l=l.slice(0,o+1):l=[l[0]];break}}}return l.reduceRight((u,c,f)=>{let v,h=!1,S=null,N=null;n&&(v=a&&c.route.id?a[c.route.id]:void 0,S=c.route.errorElement||Rp,i&&(o<0&&f===0?(sd("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,N=null):o===f&&(h=!0,N=c.route.hydrateFallbackElement||null)));let _=t.concat(l.slice(0,f+1)),L=()=>{let p;return v?p=S:h?p=N:c.route.Component?p=g.createElement(c.route.Component,null):c.route.element?p=c.route.element:p=u,g.createElement(Dp,{match:c,routeContext:{outlet:u,matches:_,isDataRoute:n!=null},children:p})};return n&&(c.route.ErrorBoundary||c.route.errorElement||f===0)?g.createElement(Tp,{location:n.location,revalidation:n.revalidation,component:S,error:v,children:L(),routeContext:{outlet:null,matches:_,isDataRoute:!0}}):L()},null)}function vi(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Mp(e){let t=g.useContext(Ln);return G(t,vi(e)),t}function $p(e){let t=g.useContext(bl);return G(t,vi(e)),t}function Ip(e){let t=g.useContext(et);return G(t,vi(e)),t}function gi(e){let t=Ip(e),n=t.matches[t.matches.length-1];return G(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function Op(){return gi("useRouteId")}function Up(){var r;let e=g.useContext(mi),t=$p("useRouteError"),n=gi("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function Bp(){let{router:e}=Mp("useNavigate"),t=gi("useNavigate"),n=g.useRef(!1);return nd(()=>{n.current=!0}),g.useCallback(async(l,a={})=>{Ke(n.current,td),n.current&&(typeof l=="number"?e.navigate(l):await e.navigate(l,{fromRouteId:t,...a}))},[e,t])}var $o={};function sd(e,t,n){!t&&!$o[e]&&($o[e]=!0,Ke(!1,n))}g.memo(Ap);function Ap({routes:e,future:t,state:n}){return ld(e,void 0,n,t)}function Hp({to:e,replace:t,state:n,relative:r}){G(Fn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:l}=g.useContext(Ge);Ke(!l,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:a}=g.useContext(et),{pathname:i}=ht(),o=rd(),u=pi(e,hi(a),i,r==="path"),c=JSON.stringify(u);return g.useEffect(()=>{o(JSON.parse(c),{replace:t,state:n,relative:r})},[o,c,r,t,n]),null}function Ot(e){G(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Wp({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:l,static:a=!1}){G(!Fn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),o=g.useMemo(()=>({basename:i,navigator:l,static:a,future:{}}),[i,l,a]);typeof n=="string"&&(n=Pn(n));let{pathname:u="/",search:c="",hash:f="",state:v=null,key:h="default"}=n,S=g.useMemo(()=>{let N=dt(u,i);return N==null?null:{location:{pathname:N,search:c,hash:f,state:v,key:h},navigationType:r}},[i,u,c,f,v,h,r]);return Ke(S!=null,`<Router basename="${i}"> is not able to match the URL "${u}${c}${f}" because it does not start with the basename, so the <Router> won't render anything.`),S==null?null:g.createElement(Ge.Provider,{value:o},g.createElement(Nr.Provider,{children:t,value:S}))}function Vp({children:e,location:t}){return Lp(va(e),t)}function va(e,t=[]){let n=[];return g.Children.forEach(e,(r,l)=>{if(!g.isValidElement(r))return;let a=[...t,l];if(r.type===g.Fragment){n.push.apply(n,va(r.props.children,a));return}G(r.type===Ot,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),G(!r.props.index||!r.props.children,"An index route cannot have child routes.");let i={id:r.props.id||a.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=va(r.props.children,a)),n.push(i)}),n}var nl="get",rl="application/x-www-form-urlencoded";function Ql(e){return e!=null&&typeof e.tagName=="string"}function bp(e){return Ql(e)&&e.tagName.toLowerCase()==="button"}function Qp(e){return Ql(e)&&e.tagName.toLowerCase()==="form"}function Kp(e){return Ql(e)&&e.tagName.toLowerCase()==="input"}function Gp(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Yp(e,t){return e.button===0&&(!t||t==="_self")&&!Gp(e)}var Wr=null;function Xp(){if(Wr===null)try{new FormData(document.createElement("form"),0),Wr=!1}catch{Wr=!0}return Wr}var Jp=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ws(e){return e!=null&&!Jp.has(e)?(Ke(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${rl}"`),null):e}function Zp(e,t){let n,r,l,a,i;if(Qp(e)){let o=e.getAttribute("action");r=o?dt(o,t):null,n=e.getAttribute("method")||nl,l=ws(e.getAttribute("enctype"))||rl,a=new FormData(e)}else if(bp(e)||Kp(e)&&(e.type==="submit"||e.type==="image")){let o=e.form;if(o==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||o.getAttribute("action");if(r=u?dt(u,t):null,n=e.getAttribute("formmethod")||o.getAttribute("method")||nl,l=ws(e.getAttribute("formenctype"))||ws(o.getAttribute("enctype"))||rl,a=new FormData(o,e),!Xp()){let{name:c,type:f,value:v}=e;if(f==="image"){let h=c?`${c}.`:"";a.append(`${h}x`,"0"),a.append(`${h}y`,"0")}else c&&a.append(c,v)}}else{if(Ql(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=nl,r=null,l=rl,i=e}return a&&l==="text/plain"&&(i=a,a=void 0),{action:r,method:n.toLowerCase(),encType:l,formData:a,body:i}}function xi(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function qp(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function em(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function tm(e,t,n){let r=await Promise.all(e.map(async l=>{let a=t.routes[l.route.id];if(a){let i=await qp(a,n);return i.links?i.links():[]}return[]}));return sm(r.flat(1).filter(em).filter(l=>l.rel==="stylesheet"||l.rel==="preload").map(l=>l.rel==="stylesheet"?{...l,rel:"prefetch",as:"style"}:{...l,rel:"prefetch"}))}function Io(e,t,n,r,l,a){let i=(u,c)=>n[c]?u.route.id!==n[c].route.id:!0,o=(u,c)=>{var f;return n[c].pathname!==u.pathname||((f=n[c].route.path)==null?void 0:f.endsWith("*"))&&n[c].params["*"]!==u.params["*"]};return a==="assets"?t.filter((u,c)=>i(u,c)||o(u,c)):a==="data"?t.filter((u,c)=>{var v;let f=r.routes[u.route.id];if(!f||!f.hasLoader)return!1;if(i(u,c)||o(u,c))return!0;if(u.route.shouldRevalidate){let h=u.route.shouldRevalidate({currentUrl:new URL(l.pathname+l.search+l.hash,window.origin),currentParams:((v=n[0])==null?void 0:v.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof h=="boolean")return h}return!0}):[]}function nm(e,t,{includeHydrateFallback:n}={}){return rm(e.map(r=>{let l=t.routes[r.route.id];if(!l)return[];let a=[l.module];return l.clientActionModule&&(a=a.concat(l.clientActionModule)),l.clientLoaderModule&&(a=a.concat(l.clientLoaderModule)),n&&l.hydrateFallbackModule&&(a=a.concat(l.hydrateFallbackModule)),l.imports&&(a=a.concat(l.imports)),a}).flat(1))}function rm(e){return[...new Set(e)]}function lm(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function sm(e,t){let n=new Set;return new Set(t),e.reduce((r,l)=>{let a=JSON.stringify(lm(l));return n.has(a)||(n.add(a),r.push({key:a,link:l})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var am=new Set([100,101,204,205]);function im(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&dt(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function ad(){let e=g.useContext(Ln);return xi(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function om(){let e=g.useContext(bl);return xi(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var yi=g.createContext(void 0);yi.displayName="FrameworkContext";function id(){let e=g.useContext(yi);return xi(e,"You must render this element inside a <HydratedRouter> element"),e}function um(e,t){let n=g.useContext(yi),[r,l]=g.useState(!1),[a,i]=g.useState(!1),{onFocus:o,onBlur:u,onMouseEnter:c,onMouseLeave:f,onTouchStart:v}=t,h=g.useRef(null);g.useEffect(()=>{if(e==="render"&&i(!0),e==="viewport"){let _=p=>{p.forEach(d=>{i(d.isIntersecting)})},L=new IntersectionObserver(_,{threshold:.5});return h.current&&L.observe(h.current),()=>{L.disconnect()}}},[e]),g.useEffect(()=>{if(r){let _=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(_)}}},[r]);let S=()=>{l(!0)},N=()=>{l(!1),i(!1)};return n?e!=="intent"?[a,h,{}]:[a,h,{onFocus:Un(o,S),onBlur:Un(u,N),onMouseEnter:Un(c,S),onMouseLeave:Un(f,N),onTouchStart:Un(v,S)}]:[!1,h,{}]}function Un(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function cm({page:e,...t}){let{router:n}=ad(),r=g.useMemo(()=>Xc(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?g.createElement(fm,{page:e,matches:r,...t}):null}function dm(e){let{manifest:t,routeModules:n}=id(),[r,l]=g.useState([]);return g.useEffect(()=>{let a=!1;return tm(e,t,n).then(i=>{a||l(i)}),()=>{a=!0}},[e,t,n]),r}function fm({page:e,matches:t,...n}){let r=ht(),{manifest:l,routeModules:a}=id(),{basename:i}=ad(),{loaderData:o,matches:u}=om(),c=g.useMemo(()=>Io(e,t,u,l,r,"data"),[e,t,u,l,r]),f=g.useMemo(()=>Io(e,t,u,l,r,"assets"),[e,t,u,l,r]),v=g.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let N=new Set,_=!1;if(t.forEach(p=>{var m;let d=l.routes[p.route.id];!d||!d.hasLoader||(!c.some(w=>w.route.id===p.route.id)&&p.route.id in o&&((m=a[p.route.id])!=null&&m.shouldRevalidate)||d.hasClientLoader?_=!0:N.add(p.route.id))}),N.size===0)return[];let L=im(e,i);return _&&N.size>0&&L.searchParams.set("_routes",t.filter(p=>N.has(p.route.id)).map(p=>p.route.id).join(",")),[L.pathname+L.search]},[i,o,r,l,c,t,e,a]),h=g.useMemo(()=>nm(f,l),[f,l]),S=dm(f);return g.createElement(g.Fragment,null,v.map(N=>g.createElement("link",{key:N,rel:"prefetch",as:"fetch",href:N,...n})),h.map(N=>g.createElement("link",{key:N,rel:"modulepreload",href:N,...n})),S.map(({key:N,link:_})=>g.createElement("link",{key:N,..._})))}function hm(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var od=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{od&&(window.__reactRouterVersion="7.6.3")}catch{}function pm({basename:e,children:t,window:n}){let r=g.useRef();r.current==null&&(r.current=ep({window:n,v5Compat:!0}));let l=r.current,[a,i]=g.useState({action:l.action,location:l.location}),o=g.useCallback(u=>{g.startTransition(()=>i(u))},[i]);return g.useLayoutEffect(()=>l.listen(o),[l,o]),g.createElement(Wp,{basename:e,children:t,location:a.location,navigationType:a.action,navigator:l})}var ud=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,cd=g.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:l,reloadDocument:a,replace:i,state:o,target:u,to:c,preventScrollReset:f,viewTransition:v,...h},S){let{basename:N}=g.useContext(Ge),_=typeof c=="string"&&ud.test(c),L,p=!1;if(typeof c=="string"&&_&&(L=c,od))try{let C=new URL(window.location.href),y=c.startsWith("//")?new URL(C.protocol+c):new URL(c),k=dt(y.pathname,N);y.origin===C.origin&&k!=null?c=k+y.search+y.hash:p=!0}catch{Ke(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let d=_p(c,{relative:l}),[m,w,x]=um(r,h),E=gm(c,{replace:i,state:o,target:u,preventScrollReset:f,relative:l,viewTransition:v});function P(C){t&&t(C),C.defaultPrevented||E(C)}let j=g.createElement("a",{...h,...x,href:L||d,onClick:p||a?t:P,ref:hm(S,w),target:u,"data-discover":!_&&n==="render"?"true":void 0});return m&&!_?g.createElement(g.Fragment,null,j,g.createElement(cm,{page:d})):j});cd.displayName="Link";var ga=g.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:l=!1,style:a,to:i,viewTransition:o,children:u,...c},f){let v=Sr(i,{relative:c.relative}),h=ht(),S=g.useContext(bl),{navigator:N,basename:_}=g.useContext(Ge),L=S!=null&&Nm(v)&&o===!0,p=N.encodeLocation?N.encodeLocation(v).pathname:v.pathname,d=h.pathname,m=S&&S.navigation&&S.navigation.location?S.navigation.location.pathname:null;n||(d=d.toLowerCase(),m=m?m.toLowerCase():null,p=p.toLowerCase()),m&&_&&(m=dt(m,_)||m);const w=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let x=d===p||!l&&d.startsWith(p)&&d.charAt(w)==="/",E=m!=null&&(m===p||!l&&m.startsWith(p)&&m.charAt(p.length)==="/"),P={isActive:x,isPending:E,isTransitioning:L},j=x?t:void 0,C;typeof r=="function"?C=r(P):C=[r,x?"active":null,E?"pending":null,L?"transitioning":null].filter(Boolean).join(" ");let y=typeof a=="function"?a(P):a;return g.createElement(cd,{...c,"aria-current":j,className:C,ref:f,style:y,to:i,viewTransition:o},typeof u=="function"?u(P):u)});ga.displayName="NavLink";var mm=g.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:l,state:a,method:i=nl,action:o,onSubmit:u,relative:c,preventScrollReset:f,viewTransition:v,...h},S)=>{let N=jm(),_=wm(o,{relative:c}),L=i.toLowerCase()==="get"?"get":"post",p=typeof o=="string"&&ud.test(o),d=m=>{if(u&&u(m),m.defaultPrevented)return;m.preventDefault();let w=m.nativeEvent.submitter,x=(w==null?void 0:w.getAttribute("formmethod"))||i;N(w||m.currentTarget,{fetcherKey:t,method:x,navigate:n,replace:l,state:a,relative:c,preventScrollReset:f,viewTransition:v})};return g.createElement("form",{ref:S,method:L,action:_,onSubmit:r?u:d,...h,"data-discover":!p&&e==="render"?"true":void 0})});mm.displayName="Form";function vm(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function dd(e){let t=g.useContext(Ln);return G(t,vm(e)),t}function gm(e,{target:t,replace:n,state:r,preventScrollReset:l,relative:a,viewTransition:i}={}){let o=rd(),u=ht(),c=Sr(e,{relative:a});return g.useCallback(f=>{if(Yp(f,t)){f.preventDefault();let v=n!==void 0?n:vr(u)===vr(c);o(e,{replace:v,state:r,preventScrollReset:l,relative:a,viewTransition:i})}},[u,o,c,n,r,t,e,l,a,i])}var xm=0,ym=()=>`__${String(++xm)}__`;function jm(){let{router:e}=dd("useSubmit"),{basename:t}=g.useContext(Ge),n=Op();return g.useCallback(async(r,l={})=>{let{action:a,method:i,encType:o,formData:u,body:c}=Zp(r,t);if(l.navigate===!1){let f=l.fetcherKey||ym();await e.fetch(f,n,l.action||a,{preventScrollReset:l.preventScrollReset,formData:u,body:c,formMethod:l.method||i,formEncType:l.encType||o,flushSync:l.flushSync})}else await e.navigate(l.action||a,{preventScrollReset:l.preventScrollReset,formData:u,body:c,formMethod:l.method||i,formEncType:l.encType||o,replace:l.replace,state:l.state,fromRouteId:n,flushSync:l.flushSync,viewTransition:l.viewTransition})},[e,t,n])}function wm(e,{relative:t}={}){let{basename:n}=g.useContext(Ge),r=g.useContext(et);G(r,"useFormAction must be used inside a RouteContext");let[l]=r.matches.slice(-1),a={...Sr(e||".",{relative:t})},i=ht();if(e==null){a.search=i.search;let o=new URLSearchParams(a.search),u=o.getAll("index");if(u.some(f=>f==="")){o.delete("index"),u.filter(v=>v).forEach(v=>o.append("index",v));let f=o.toString();a.search=f?`?${f}`:""}}return(!e||e===".")&&l.route.index&&(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(a.pathname=a.pathname==="/"?n:at([n,a.pathname])),vr(a)}function Nm(e,t={}){let n=g.useContext(ed);G(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=dd("useViewTransitionState"),l=Sr(e,{relative:t.relative});if(!n.isTransitioning)return!1;let a=dt(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=dt(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Pl(l.pathname,i)!=null||Pl(l.pathname,a)!=null}[...am];function Sm(){const[e,t]=g.useState(!1);ht();const n=[{path:"/migration",label:"Migration Tool",icon:"🚀",description:"Công cụ migration file từ Drive sang Lark"},{path:"/download",label:"Download Files",icon:"📥",description:"Download files từ Google Drive về local"},{path:"/storage",label:"Storage Comparison",icon:"📊",description:"So sánh dung lượng đã download vs Google Drive"},{path:"/users",label:"Users Overview",icon:"👥",description:"Xem tổng quan users và file structure"},{path:"/user-management",label:"User Management",icon:"👤",description:"Quản lý người dùng và scan files từ Workspace"}],r=()=>{t(!e)};return s.jsxs("nav",{className:"navigation",children:[s.jsxs("div",{className:"nav-container",children:[s.jsxs("div",{className:"nav-brand",children:[s.jsx("div",{className:"brand-icon",children:"🏢"}),s.jsxs("div",{className:"brand-text",children:[s.jsx("div",{className:"brand-title",children:"Drive to Lark"}),s.jsx("div",{className:"brand-subtitle",children:"Migration Platform"})]})]}),s.jsx("div",{className:"nav-links",children:n.map(l=>s.jsxs(ga,{to:l.path,className:({isActive:a})=>`nav-link ${a?"active":""}`,title:l.description,children:[s.jsx("span",{className:"nav-icon",children:l.icon}),s.jsx("span",{className:"nav-text",children:l.label})]},l.path))}),s.jsxs("div",{className:"nav-actions",children:[s.jsx("button",{className:"action-btn",title:"Thông báo",children:"🔔"}),s.jsx("button",{className:"action-btn",title:"Cài đặt",children:"⚙️"}),s.jsx("div",{className:"user-menu",children:s.jsx("button",{className:"user-avatar",title:"User menu",children:"👤"})})]}),s.jsx("button",{className:"mobile-menu-btn",onClick:r,"aria-label":"Toggle mobile menu",children:e?"✕":"☰"})]}),e&&s.jsx("div",{className:"mobile-menu",children:s.jsxs("div",{className:"mobile-menu-content",children:[n.map(l=>s.jsxs(ga,{to:l.path,className:({isActive:a})=>`mobile-nav-link ${a?"active":""}`,onClick:()=>t(!1),children:[s.jsx("span",{className:"mobile-nav-icon",children:l.icon}),s.jsxs("div",{className:"mobile-nav-text",children:[s.jsx("div",{className:"mobile-nav-label",children:l.label}),s.jsx("div",{className:"mobile-nav-desc",children:l.description})]})]},l.path)),s.jsxs("div",{className:"mobile-actions",children:[s.jsx("button",{className:"mobile-action-btn",children:"🔔 Thông báo"}),s.jsx("button",{className:"mobile-action-btn",children:"⚙️ Cài đặt"})]})]})})]})}const ji=async(e,t={})=>{try{const n=await fetch(e,{headers:{"Content-Type":"application/json",...t.headers},...t});if(!n.ok){let r;try{r=await n.json()}catch{r={message:n.statusText}}const l=km(n.status,r.message),a=new Error(l);throw a.status=n.status,a.statusText=n.statusText,a.originalMessage=r.message,a.response={status:n.status,statusText:n.statusText,data:r},a}try{return await n.json()}catch{return await n.text()}}catch(n){throw n.status||(n.message=`Lỗi kết nối: ${n.message}`),n}},km=(e,t)=>{const r={400:"Yêu cầu không hợp lệ",401:"Không có quyền truy cập",403:"Bị cấm truy cập",404:"Không tìm thấy tài nguyên",408:"Hết thời gian chờ",429:"Quá nhiều yêu cầu, vui lòng thử lại sau",500:"Lỗi máy chủ nội bộ",502:"Lỗi gateway",503:"Dịch vụ không khả dụng",504:"Hết thời gian chờ gateway"}[e]||`Lỗi HTTP ${e}`;return t&&t!=="Internal Server Error"&&t!=="Bad Request"?`${r}: ${t}`:r},fe=(e,t={})=>ji(e,{method:"GET",...t}),We=(e,t=null,n={})=>ji(e,{method:"POST",body:t?JSON.stringify(t):null,...n}),Cm=(e,t={})=>ji(e,{method:"DELETE",...t}),Me=e=>{var t,n,r;if(typeof e=="string")return{message:e,details:null,code:null};if(e instanceof Error){let l=[];return e.originalMessage&&e.originalMessage!==e.message&&l.push(`Thông báo gốc: ${e.originalMessage}`),e.status&&l.push(`Mã lỗi HTTP: ${e.status}`),(t=e.response)!=null&&t.data&&l.push(`Chi tiết API: ${JSON.stringify(e.response.data,null,2)}`),{message:e.message,details:l.length>0?l.join(`

`):null,code:e.status||e.code||null}}return e.response?{message:((n=e.response.data)==null?void 0:n.message)||e.response.statusText||"Lỗi API",details:((r=e.response.data)==null?void 0:r.details)||`HTTP ${e.response.status}`,code:e.response.status}:{message:e.message||"Lỗi không xác định",details:JSON.stringify(e,null,2),code:null}},Yt=({error:e,title:t="Đã xảy ra lỗi",onRetry:n=null,onDismiss:r=null,showDetails:l=!0,className:a=""})=>{if(!e)return null;const o=(()=>{var u,c;return typeof e=="string"?{message:e,details:null,code:null}:e instanceof Error?{message:e.message,details:e.stack,code:e.code||null}:e.response?{message:((u=e.response.data)==null?void 0:u.message)||e.response.statusText||"Lỗi API",details:((c=e.response.data)==null?void 0:c.details)||`HTTP ${e.response.status}`,code:e.response.status}:e.status?{message:e.message||"Lỗi kết nối",details:`HTTP ${e.status}: ${e.statusText}`,code:e.status}:{message:e.message||"Lỗi không xác định",details:JSON.stringify(e,null,2),code:null}})();return s.jsx("div",{className:`error-display ${a}`,children:s.jsxs("div",{className:"error-content",children:[s.jsxs("div",{className:"error-header",children:[s.jsx("div",{className:"error-icon",children:"❌"}),s.jsx("div",{className:"error-title",children:t}),r&&s.jsx("button",{onClick:r,className:"error-dismiss","aria-label":"Đóng",children:"✕"})]}),s.jsxs("div",{className:"error-body",children:[s.jsx("div",{className:"error-message",children:o.message}),o.code&&s.jsxs("div",{className:"error-code",children:["Mã lỗi: ",o.code]}),l&&o.details&&s.jsxs("details",{className:"error-details",children:[s.jsx("summary",{children:"Chi tiết lỗi"}),s.jsx("pre",{className:"error-details-content",children:o.details})]})]}),n&&s.jsx("div",{className:"error-actions",children:s.jsx("button",{onClick:n,className:"btn btn-primary btn-small",children:"🔄 Thử lại"})})]})})},Em=({userEmail:e,onFolderSelected:t})=>{const[n,r]=g.useState("/"),[l,a]=g.useState("root"),[i,o]=g.useState([]),[u,c]=g.useState(!1),[f,v]=g.useState(null),[h,S]=g.useState([{id:"root",name:"My Drive",path:"/"}]);g.useEffect(()=>{e&&e!=="ALL_USERS"&&N(l)},[l,e]);const N=async m=>{if(!(!e||e==="ALL_USERS")){c(!0),v(null);try{const w=await fe(`/api/folders/list?userEmail=${encodeURIComponent(e)}&parentId=${m}`);o(w.folders||[])}catch(w){console.error("Error loading folders:",w),v(w),o([])}finally{c(!1)}}},_=async m=>{try{const x=(await fe(`/api/folders/resolve-id?userEmail=${encodeURIComponent(e)}&folderId=${m.id}`)).path||`${n}/${m.name}`.replace(/\/+/g,"/");a(m.id),r(x),S(E=>[...E,{id:m.id,name:m.name,path:x}])}catch(w){console.error("Error navigating to folder:",w),v(w)}},L=()=>{if(h.length>1){const m=h.slice(0,-1),w=m[m.length-1];S(m),a(w.id),r(w.path)}},p=m=>{if(m<h.length-1){const w=h.slice(0,m+1),x=w[w.length-1];S(w),a(x.id),r(x.path)}},d=()=>{const m=h[h.length-1];t({id:m.id,name:m.name,path:m.path})};return e?e==="ALL_USERS"?s.jsx("div",{className:"folder-browser",children:s.jsx("p",{className:"info-message",children:'📁 Folder browsing is not available when "All Users" is selected. The scan will include all accessible files from all users.'})}):s.jsxs("div",{className:"folder-browser",children:[s.jsxs("div",{className:"browser-header",children:[s.jsx("div",{className:"breadcrumb",children:h.map((m,w)=>s.jsxs("span",{className:"breadcrumb-item",children:[w>0&&s.jsx("span",{className:"breadcrumb-separator",children:"/"}),s.jsx("button",{onClick:()=>p(w),className:`breadcrumb-link ${w===h.length-1?"current":""}`,disabled:w===h.length-1,children:m.name})]},m.id))}),s.jsxs("div",{className:"browser-actions",children:[s.jsx("button",{onClick:L,disabled:h.length<=1,className:"btn btn-secondary btn-small",children:"⬆️ Up"}),s.jsx("button",{onClick:d,className:"btn btn-primary btn-small",disabled:l==="root",children:"✅ Select This Folder"})]})]}),s.jsxs("div",{className:"folder-list",children:[u&&s.jsxs("div",{className:"loading-state",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading folders..."})]}),f&&s.jsx(Yt,{error:f,title:"Lỗi tải danh sách thư mục",onRetry:()=>N(l),onDismiss:()=>v(null),className:"inline compact"}),!u&&!f&&i.length===0&&s.jsx("div",{className:"empty-state",children:s.jsx("p",{children:"📁 No folders found in this directory"})}),!u&&!f&&i.length>0&&s.jsx("div",{className:"folders-grid",children:i.map(m=>s.jsxs("div",{className:"folder-item",onClick:()=>_(m),children:[s.jsx("div",{className:"folder-icon",children:"📁"}),s.jsxs("div",{className:"folder-info",children:[s.jsx("div",{className:"folder-name",children:m.name}),s.jsx("div",{className:"folder-meta",children:m.modifiedTime&&s.jsxs("span",{className:"folder-date",children:["Modified: ",new Date(m.modifiedTime).toLocaleDateString()]})})]}),s.jsx("div",{className:"folder-arrow",children:"➡️"})]},m.id))})]}),s.jsx("div",{className:"browser-footer",children:s.jsxs("div",{className:"current-selection",children:[s.jsx("strong",{children:"Current folder:"})," ",n]})})]}):s.jsx("div",{className:"folder-browser",children:s.jsx("p",{className:"info-message",children:"Please select a user to browse folders"})})},_m=({onScopeSelected:e,userEmail:t})=>{const[n,r]=g.useState("all"),[l,a]=g.useState(null),[i,o]=g.useState(10),[u,c]=g.useState(!0),[f,v]=g.useState({includeGoogleDocs:!0,includeImages:!0,includePDFs:!0,includeOfficeFiles:!0,includeOthers:!1}),h=d=>{r(d),d==="all"&&a(null)},S=d=>{a(d),r("folder")},N=(d,m)=>{v(w=>({...w,[d]:m}))},_=()=>{const d=[];return f.includeGoogleDocs&&d.push("application/vnd.google-apps.document","application/vnd.google-apps.spreadsheet","application/vnd.google-apps.presentation"),f.includeImages&&d.push("image/jpeg","image/png","image/gif","image/bmp"),f.includePDFs&&d.push("application/pdf"),f.includeOfficeFiles&&d.push("application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-powerpoint","application/vnd.openxmlformats-officedocument.presentationml.presentation"),d.push("application/vnd.google-apps.folder"),d},L=()=>{if(!t){alert("Please enter your email address");return}const d={maxDepth:i,includeSharedDrives:u,filterMimeTypes:_(),folderId:n==="folder"?l==null?void 0:l.id:null,folderPath:n==="folder"?l==null?void 0:l.path:null};e(n,d)},p=()=>!t||n==="folder"&&!l&&t!=="ALL_USERS"?!1:Object.values(f).some(d=>d);return s.jsxs("div",{className:"scope-selector",children:[s.jsx("h2",{children:"📁 Select Migration Scope"}),s.jsxs("div",{className:"scope-options",children:[s.jsx("div",{className:"scope-option",children:s.jsxs("label",{className:"radio-label",children:[s.jsx("input",{type:"radio",name:"scope",value:"all",checked:n==="all",onChange:d=>h(d.target.value)}),s.jsx("span",{className:"radio-custom"}),s.jsxs("div",{className:"option-content",children:[s.jsx("h3",{children:"🌐 Entire Drive"}),s.jsx("p",{children:"Scan and migrate all files from your Google Drive"})]})]})}),s.jsx("div",{className:"scope-option",children:s.jsxs("label",{className:"radio-label",children:[s.jsx("input",{type:"radio",name:"scope",value:"folder",checked:n==="folder",onChange:d=>h(d.target.value)}),s.jsx("span",{className:"radio-custom"}),s.jsxs("div",{className:"option-content",children:[s.jsx("h3",{children:"📂 Specific Folder"}),s.jsx("p",{children:"Choose a specific folder to migrate"})]})]})})]}),n==="folder"&&s.jsxs("div",{className:"folder-selection",children:[s.jsx("h3",{children:"Choose Folder"}),l?s.jsxs("div",{className:"selected-folder",children:[s.jsx("span",{className:"folder-icon",children:"📁"}),s.jsx("span",{className:"folder-path",children:l.path||l.name}),s.jsx("button",{onClick:()=>a(null),className:"btn btn-secondary btn-small",children:"Change"})]}):s.jsx(Em,{userEmail:t,onFolderSelected:S})]}),s.jsxs("div",{className:"scan-options",children:[s.jsx("h3",{children:"⚙️ Scan Options"}),s.jsxs("div",{className:"option-group",children:[s.jsxs("label",{className:"option-label",children:["Maximum Folder Depth:",s.jsx("input",{type:"number",min:"1",max:"100",value:i,onChange:d=>o(parseInt(d.target.value)),className:"number-input"})]}),s.jsx("small",{children:"Limit how deep to scan nested folders (1-100)"})]}),s.jsxs("div",{className:"option-group",children:[s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",checked:u,onChange:d=>c(d.target.checked)}),s.jsx("span",{className:"checkbox-custom"}),"Include Shared Drives"]}),s.jsx("small",{children:"Scan files from shared/team drives"})]})]}),s.jsxs("div",{className:"file-filters",children:[s.jsx("h3",{children:"📄 File Type Filters"}),s.jsxs("div",{className:"filter-grid",children:[s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",checked:f.includeGoogleDocs,onChange:d=>N("includeGoogleDocs",d.target.checked)}),s.jsx("span",{className:"checkbox-custom"}),"Google Docs, Sheets, Slides"]}),s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",checked:f.includeImages,onChange:d=>N("includeImages",d.target.checked)}),s.jsx("span",{className:"checkbox-custom"}),"Images (JPG, PNG, GIF)"]}),s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",checked:f.includePDFs,onChange:d=>N("includePDFs",d.target.checked)}),s.jsx("span",{className:"checkbox-custom"}),"PDF Documents"]}),s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",checked:f.includeOfficeFiles,onChange:d=>N("includeOfficeFiles",d.target.checked)}),s.jsx("span",{className:"checkbox-custom"}),"Office Files (Word, Excel, PowerPoint)"]}),s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",checked:f.includeOthers,onChange:d=>N("includeOthers",d.target.checked)}),s.jsx("span",{className:"checkbox-custom"}),"Other File Types"]})]})]}),s.jsx("div",{className:"action-buttons",children:s.jsx("button",{onClick:L,disabled:!p(),className:"btn btn-primary btn-large",children:"🔍 Start Scanning"})}),!p()&&s.jsxs("div",{className:"validation-message",children:[!t&&s.jsx("p",{children:"⚠️ Please select a user"}),n==="folder"&&!l&&t!=="ALL_USERS"&&s.jsx("p",{children:"⚠️ Please select a folder"}),!Object.values(f).some(d=>d)&&s.jsx("p",{children:"⚠️ Please select at least one file type"})]})]})},fd=({tree:e,selectedFiles:t,onFileSelect:n,onSelectAll:r,onSelectAllToggle:l})=>{const[a,i]=g.useState(new Set),o=x=>{const E=new Set(a);E.has(x)?E.delete(x):E.add(x),i(E)},u=x=>t.some(E=>E.id===x),c=x=>{let E=[];return x.children&&x.children.forEach(P=>{P.type==="file"?E.push(P):P.type==="folder"&&(E=E.concat(c(P)))}),E},f=x=>{const E=c(x);if(E.length===0)return"none";const P=E.filter(j=>t.some(C=>C.id===j.id));return P.length===0?"none":P.length===E.length?"all":"some"},v=(x,E)=>{const P=c(x);if(E){const j=[...t];P.forEach(C=>{t.some(y=>y.id===C.id)||j.push(C)}),r(j)}else{const j=P.map(y=>y.id),C=t.filter(y=>!j.includes(y.id));r(C)}},h=(x,E)=>E==="folder"?"📁":x!=null&&x.startsWith("image/")?"🖼️":x!=null&&x.startsWith("video/")?"🎥":x!=null&&x.startsWith("audio/")?"🎵":x!=null&&x.includes("pdf")?"📄":x!=null&&x.includes("document")||x!=null&&x.includes("word")?"📝":x!=null&&x.includes("spreadsheet")||x!=null&&x.includes("excel")?"📊":x!=null&&x.includes("presentation")||x!=null&&x.includes("powerpoint")?"📈":x!=null&&x.includes("zip")||x!=null&&x.includes("archive")?"📦":"📄",S=x=>{if(!x)return"0 B";const E=["B","KB","MB","GB"],P=Math.floor(Math.log(x)/Math.log(1024));return Math.round(x/Math.pow(1024,P)*100)/100+" "+E[P]},N=x=>{switch(x){case"downloaded":return"✅";case"failed":return"❌";case"not_downloaded":case null:case void 0:return"⏳";default:return"⏳"}},_=x=>{switch(x){case"downloaded":return"Downloaded";case"failed":return"Failed";case"not_downloaded":case null:case void 0:return"Not Downloaded";default:return"Not Downloaded"}},L=(x,E=0)=>{const P=x.type==="folder",j=a.has(x.id),C=x.children&&x.children.length>0,y=!P&&u(x.id),k=P?f(x):null,T=P&&c(x).length>0;return s.jsxs("div",{className:"tree-node",children:[s.jsxs("div",{className:`tree-item ${y?"selected":""} ${k==="all"?"folder-all-selected":k==="some"?"folder-some-selected":""}`,style:{paddingLeft:`${E*20+10}px`},children:[P&&s.jsx("button",{className:`tree-toggle ${C?"":"empty"}`,onClick:()=>o(x.id),disabled:!C,children:C?j?"▼":"▶":"○"}),s.jsxs("label",{className:"tree-checkbox",children:[s.jsx("input",{type:"checkbox",checked:P?k==="all":y,ref:P?z=>{z&&(z.indeterminate=k==="some")}:null,onChange:z=>{P?v(x,z.target.checked):n(x,z.target.checked)},disabled:P&&!T}),s.jsx("span",{className:"checkbox-custom"})]}),s.jsx("span",{className:"tree-icon",children:h(x.mime_type,x.type)}),s.jsx("span",{className:"tree-name",title:x.name,children:x.name}),s.jsx("div",{className:"tree-info",children:P?s.jsxs("span",{className:"folder-stats",children:[x.fileCount>0&&`${x.fileCount} files`,x.fileCount>0&&x.folderCount>0&&", ",x.folderCount>0&&`${x.folderCount} folders`,x.totalSize>0&&` (${S(x.totalSize)})`,k==="some"&&s.jsx("span",{className:"selection-indicator",children:" - Partially Selected"}),k==="all"&&T&&s.jsx("span",{className:"selection-indicator",children:" - All Selected"})]}):s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"file-size",children:S(x.size)}),x.modified_time&&s.jsx("span",{className:"file-date",children:new Date(x.modified_time).toLocaleDateString()}),s.jsx("span",{className:`download-status ${x.download_status||"not_downloaded"}`,title:_(x.download_status),children:N(x.download_status)})]})})]}),P&&j&&C&&s.jsx("div",{className:"tree-children",children:x.children.sort((z,B)=>z.type!==B.type?z.type==="folder"?-1:1:z.name.toLowerCase().localeCompare(B.name.toLowerCase())).map(z=>L(z,E+1))})]},x.id)},p=x=>{let E=[];return x.forEach(P=>{P.type==="file"&&E.push(P),P.children&&(E=E.concat(p(P.children)))}),E},d=p(e),m=t.length,w=d.length;return s.jsxs("div",{className:"tree-view",children:[s.jsxs("div",{className:"tree-header",children:[s.jsx("div",{className:"tree-controls",children:s.jsxs("label",{className:"select-all-checkbox",children:[s.jsx("input",{type:"checkbox",checked:m===w&&w>0,onChange:x=>{const E=x.target.checked;r(E?d:[]),l&&l(E)}}),s.jsx("span",{className:"checkbox-custom"}),s.jsxs("span",{children:["Select All (",w," files)"]})]})}),s.jsx("div",{className:"tree-stats",children:s.jsxs("span",{children:["Selected: ",m," / ",w]})})]}),s.jsx("div",{className:"tree-content",children:e.sort((x,E)=>x.type!==E.type?x.type==="folder"?-1:1:x.name.toLowerCase().localeCompare(E.name.toLowerCase())).map(x=>L(x))})]})},hd=({stats:e,selectedFiles:t})=>{const n=o=>{if(!o)return"0 B";const u=["B","KB","MB","GB","TB"],c=Math.floor(Math.log(o)/Math.log(1024));return Math.round(o/Math.pow(1024,c)*100)/100+" "+u[c]},r=()=>t.reduce((o,u)=>o+(u.size||0),0),l=()=>e!=null&&e.fileTypes?Object.entries(e.fileTypes).sort(([,o],[,u])=>u-o).slice(0,5).map(([o,u])=>({type:o.split("/").pop()||o,count:u,fullType:o})):[];if(!e)return s.jsx("div",{className:"statistics",children:s.jsxs("div",{className:"stats-loading",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading statistics..."})]})});const a=l(),i=r();return s.jsxs("div",{className:"statistics",children:[s.jsx("h3",{children:"📊 Scan Statistics"}),s.jsxs("div",{className:"stats-grid",children:[s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"📁"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-number",children:e.totalFolders.toLocaleString()}),s.jsx("div",{className:"stat-label",children:"Folders"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"📄"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-number",children:e.totalFiles.toLocaleString()}),s.jsx("div",{className:"stat-label",children:"Files"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"💾"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-number",children:n(e.totalSize)}),s.jsx("div",{className:"stat-label",children:"Total Size"})]})]}),s.jsxs("div",{className:"stat-card selected",children:[s.jsx("div",{className:"stat-icon",children:"✅"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-number",children:t.length.toLocaleString()}),s.jsx("div",{className:"stat-label",children:"Selected Files"})]})]}),s.jsxs("div",{className:"stat-card selected",children:[s.jsx("div",{className:"stat-icon",children:"📦"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-number",children:n(i)}),s.jsx("div",{className:"stat-label",children:"Selected Size"})]})]}),s.jsxs("div",{className:"stat-card selected",children:[s.jsx("div",{className:"stat-icon",children:"📈"}),s.jsxs("div",{className:"stat-content",children:[s.jsxs("div",{className:"stat-number",children:[e.totalFiles>0?Math.round(t.length/e.totalFiles*100):0,"%"]}),s.jsx("div",{className:"stat-label",children:"Selection Rate"})]})]})]}),a.length>0&&s.jsxs("div",{className:"file-types-section",children:[s.jsx("h4",{children:"📋 Top File Types"}),s.jsx("div",{className:"file-types-list",children:a.map(({type:o,count:u,fullType:c})=>s.jsxs("div",{className:"file-type-item",children:[s.jsxs("div",{className:"file-type-info",children:[s.jsx("span",{className:"file-type-name",title:c,children:o}),s.jsx("span",{className:"file-type-count",children:u.toLocaleString()})]}),s.jsx("div",{className:"file-type-bar",children:s.jsx("div",{className:"file-type-progress",style:{width:`${u/e.totalFiles*100}%`}})})]},c))})]})]})},Pm=({scanSession:e,onFilesSelected:t,selectedFiles:n,onProceedToMigration:r,onTreeDataLoaded:l,onSelectAllToggle:a})=>{const[i,o]=g.useState([]),[u,c]=g.useState(null),[f,v]=g.useState(!0),[h,S]=g.useState(null),[N,_]=g.useState("tree"),[L,p]=g.useState({search:"",mimeType:"all",downloadStatus:"all"});g.useEffect(()=>{e!=null&&e.id&&d()},[e,L]);const d=async()=>{if(!(e!=null&&e.id)){console.log("No scan session ID available");return}console.log("Loading tree data for session:",e.id),v(!0),S(null);try{const E=new URLSearchParams({sessionId:e.id,...L});console.log("Calling API:",`/api/scan/files/tree?${E}`);const P=await fe(`/api/scan/files/tree?${E}`);console.log("Tree data received:",P),o(P.tree||[]),c(P.stats||null),l&&l(P.tree||[])}catch(E){console.error("Error loading tree data:",E),S(E),o([]),c(null)}finally{v(!1)}},m=(E,P)=>{let j;P?j=[...n,E]:j=n.filter(C=>C.id!==E.id),t(j)},w=E=>{t(E)},x=(E,P)=>{p(j=>({...j,[E]:P}))};return f?s.jsx("div",{className:"file-list",children:s.jsxs("div",{className:"loading-state",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading files..."})]})}):h?s.jsx("div",{className:"file-list",children:s.jsx(Yt,{error:h,title:"Lỗi tải danh sách file",onRetry:d,onDismiss:()=>S(null)})}):s.jsxs("div",{className:"file-list",children:[s.jsx("h2",{children:"📋 Select Files to Migrate"}),s.jsx(hd,{stats:u,selectedFiles:n}),s.jsx("div",{className:"file-filters",children:s.jsxs("div",{className:"filter-row",children:[s.jsx("input",{type:"text",placeholder:"Search files...",value:L.search,onChange:E=>x("search",E.target.value),className:"search-input"}),s.jsxs("select",{value:L.mimeType,onChange:E=>x("mimeType",E.target.value),className:"filter-select",children:[s.jsx("option",{value:"all",children:"All File Types"}),s.jsx("option",{value:"application/vnd.google-apps.document",children:"Google Docs"}),s.jsx("option",{value:"application/vnd.google-apps.spreadsheet",children:"Google Sheets"}),s.jsx("option",{value:"application/vnd.google-apps.presentation",children:"Google Slides"}),s.jsx("option",{value:"application/pdf",children:"PDF Files"}),s.jsx("option",{value:"image/",children:"Images"}),s.jsx("option",{value:"application/vnd.google-apps.folder",children:"Folders"})]}),s.jsxs("select",{value:L.downloadStatus,onChange:E=>x("downloadStatus",E.target.value),className:"filter-select",children:[s.jsx("option",{value:"all",children:"All Download Status"}),s.jsx("option",{value:"not_downloaded",children:"Not Downloaded"}),s.jsx("option",{value:"downloaded",children:"Downloaded"}),s.jsx("option",{value:"failed",children:"Failed"})]})]})}),s.jsx(fd,{tree:i,selectedFiles:n,onFileSelect:m,onSelectAll:w,onSelectAllToggle:a}),s.jsx("div",{className:"action-buttons",children:s.jsxs("button",{onClick:r,disabled:n.length===0,className:"btn btn-primary btn-large",children:["Continue with ",n.length," Selected Files"]})})]})},Lm=({scanSession:e,onCancel:t})=>{const[n,r]=g.useState(0),[l,a]=g.useState(null),[i,o]=g.useState(null);g.useEffect(()=>{e&&u()},[e]);const u=()=>{if(!e)return;const h=e.scanned_files||0,S=e.total_files||0;if(S>0?r(h/S*100):r(0),e.started_at&&h>0){const N=new Date(e.started_at),L=new Date-N,p=h/L;if(S>h&&p>0){const m=(S-h)/p;a(m)}}},c=h=>{if(!h)return"Calculating...";const S=Math.floor(h/1e3),N=Math.floor(S/60),_=Math.floor(N/60);return _>0?`${_}h ${N%60}m`:N>0?`${N}m ${S%60}s`:`${S}s`},f=h=>{if(!h)return"0 B";const S=["B","KB","MB","GB","TB"];let N=h,_=0;for(;N>=1024&&_<S.length-1;)N/=1024,_++;return`${N.toFixed(1)} ${S[_]}`},v=async()=>{if(o(null),e!=null&&e.id)try{await We(`/api/scan/cancel/${e.id}`)}catch(h){console.error("Error cancelling scan:",h),o(h);return}t()};return e?s.jsxs("div",{className:"scan-progress",children:[s.jsx("h2",{children:"🔍 Scanning Your Drive"}),s.jsxs("div",{className:"progress-overview",children:[s.jsxs("div",{className:"progress-bar-container",children:[s.jsx("div",{className:"progress-bar",children:s.jsx("div",{className:"progress-fill",style:{width:`${Math.min(n,100)}%`}})}),s.jsx("div",{className:"progress-text",children:e.status==="completed"?"100%":`${Math.round(n)}%`})]}),s.jsxs("div",{className:"scan-stats",children:[s.jsxs("div",{className:"stat-item",children:[s.jsx("div",{className:"stat-value",children:e.scanned_files||0}),s.jsx("div",{className:"stat-label",children:"Files Scanned"})]}),s.jsxs("div",{className:"stat-item",children:[s.jsx("div",{className:"stat-value",children:e.folders_processed||0}),s.jsx("div",{className:"stat-label",children:"Folders Processed"})]}),s.jsxs("div",{className:"stat-item",children:[s.jsx("div",{className:"stat-value",children:e.current_depth||0}),s.jsx("div",{className:"stat-label",children:"Current Depth"})]}),s.jsxs("div",{className:"stat-item",children:[s.jsx("div",{className:"stat-value",children:f(e.total_size||0)}),s.jsx("div",{className:"stat-label",children:"Total Size"})]})]})]}),s.jsxs("div",{className:"scan-details",children:[s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Status:"}),s.jsxs("span",{className:`detail-value status-${e.status}`,children:[e.status==="running"&&"🔄 Scanning...",e.status==="completed"&&"✅ Completed",e.status==="failed"&&"❌ Failed",e.status==="cancelled"&&"🛑 Cancelled"]})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Started:"}),s.jsx("span",{className:"detail-value",children:e.started_at?new Date(e.started_at).toLocaleString():"N/A"})]}),e.status==="running"&&l&&s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Estimated Time Remaining:"}),s.jsx("span",{className:"detail-value",children:c(l)})]}),e.scan_duration&&s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Duration:"}),s.jsx("span",{className:"detail-value",children:c(e.scan_duration)})]}),e.error_message&&s.jsxs("div",{className:"detail-row error",children:[s.jsx("span",{className:"detail-label",children:"Error:"}),s.jsx("span",{className:"detail-value",children:e.error_message})]})]}),i&&s.jsx(Yt,{error:i,title:"Lỗi hủy quét",onDismiss:()=>o(null),onRetry:v,className:"inline compact"}),e.status==="running"&&s.jsx("div",{className:"scan-actions",children:s.jsx("button",{onClick:v,className:"btn btn-secondary",children:"🛑 Hủy quét"})}),e.status==="completed"&&s.jsx("div",{className:"scan-summary",children:s.jsxs("div",{className:"summary-card",children:[s.jsx("h3",{children:"✅ Scan Completed Successfully!"}),s.jsxs("p",{children:["Found ",s.jsx("strong",{children:e.total_files})," files totaling ",s.jsx("strong",{children:f(e.total_size)})]}),s.jsxs("p",{children:["Scanned ",s.jsx("strong",{children:e.folders_processed})," folders with maximum depth of ",s.jsx("strong",{children:e.current_depth})," levels"]})]})}),e.status==="failed"&&s.jsx(Yt,{error:new Error(e.error_message||"Đã xảy ra lỗi không xác định trong quá trình quét."),title:"Quét thất bại",onRetry:t,showDetails:!0})]}):s.jsx("div",{className:"scan-progress",children:s.jsxs("div",{className:"loading-state",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Initializing scan..."})]})})},Fm=({value:e,onChange:t,className:n="",disabled:r=!1})=>{const[l,a]=g.useState([]),[i,o]=g.useState(!1),[u,c]=g.useState(!1),[f,v]=g.useState(""),[h,S]=g.useState(null),N=g.useRef(null),_=g.useRef(null);g.useEffect(()=>{L()},[]),g.useEffect(()=>{const C=y=>{N.current&&!N.current.contains(y.target)&&c(!1)};return document.addEventListener("mousedown",C),()=>{document.removeEventListener("mousedown",C)}},[]);const L=async()=>{o(!0),S(null);try{const y=(await fe("/api/scan/users")).users||[],k=[{id:"ALL_USERS",email:"ALL_USERS",fullName:"Tất cả users",isAllOption:!0},...y.map(T=>({id:T.userId||T.id,email:T.email,fullName:T.fullName||T.email,givenName:T.givenName,familyName:T.familyName,lastLoginTime:T.lastLoginTime,suspended:T.suspended}))];a(k)}catch(C){console.error("Error loading users:",C),S("Không thể tải danh sách users")}finally{o(!1)}},p=l.filter(C=>{var k,T,z,B;if(!f)return!0;const y=f.toLowerCase();return((k=C.email)==null?void 0:k.toLowerCase().includes(y))||((T=C.fullName)==null?void 0:T.toLowerCase().includes(y))||((z=C.givenName)==null?void 0:z.toLowerCase().includes(y))||((B=C.familyName)==null?void 0:B.toLowerCase().includes(y))}),d=l.find(C=>C.email===e),m=d?d.isAllOption?d.fullName:d.email:e||"",w=C=>{t(C.email),c(!1),v("")},x=()=>{r||c(!u)},E=()=>{r||c(!0)},P=C=>{v(C.target.value),c(!0)},j=C=>{C.key==="Escape"&&(c(!1),v(""))};return s.jsxs("div",{className:`user-selector ${n}`,ref:N,children:[s.jsxs("div",{className:"user-selector-input-container",children:[s.jsx("input",{ref:_,type:"text",className:"user-selector-input",placeholder:i?"Đang tải users...":"Chọn user...",value:u?f:m,onChange:P,onClick:x,onFocus:E,onKeyDown:j,disabled:r||i,autoComplete:"off"}),s.jsx("div",{className:`user-selector-arrow ${u?"open":""}`,onClick:x,children:"▼"})]}),u&&s.jsxs("div",{className:"user-selector-dropdown",children:[i&&s.jsxs("div",{className:"user-selector-loading",children:[s.jsx("div",{className:"spinner"}),"Đang tải users..."]}),h&&s.jsxs("div",{className:"user-selector-error",children:[h,s.jsx("button",{className:"retry-btn",onClick:L,type:"button",children:"Thử lại"})]}),!i&&!h&&p.length===0&&s.jsx("div",{className:"user-selector-no-results",children:"Không tìm thấy user nào"}),!i&&!h&&p.length>0&&s.jsx("div",{className:"user-selector-list",children:p.map(C=>s.jsx("div",{className:`user-selector-item ${C.isAllOption?"all-users-option":""} ${C.email===e?"selected":""} ${C.suspended?"suspended":""}`,onClick:()=>w(C),children:s.jsxs("div",{className:"user-info",children:[s.jsx("div",{className:"user-email",children:C.isAllOption?s.jsxs("span",{className:"all-users-label",children:["🌐 ",C.fullName]}):s.jsxs(s.Fragment,{children:[C.email,C.suspended&&s.jsx("span",{className:"suspended-badge",children:"Suspended"})]})}),!C.isAllOption&&C.fullName&&C.fullName!==C.email&&s.jsx("div",{className:"user-name",children:C.fullName}),!C.isAllOption&&C.lastLoginTime&&s.jsxs("div",{className:"user-last-login",children:["Đăng nhập cuối: ",new Date(C.lastLoginTime).toLocaleString("vi-VN")]})]})},C.id))})]})]})},Rm=({message:e,type:t="error",duration:n=5e3,onClose:r,showDetails:l=!1,details:a=null})=>{const[i,o]=g.useState(!0),[u,c]=g.useState(!1);g.useEffect(()=>{if(n>0){const S=setTimeout(()=>{f()},n);return()=>clearTimeout(S)}},[n]);const f=()=>{o(!1),setTimeout(()=>{r&&r()},300)},v=()=>{switch(t){case"success":return"✅";case"warning":return"⚠️";case"info":return"ℹ️";case"error":default:return"❌"}},h=()=>{switch(t){case"success":return"toast-success";case"warning":return"toast-warning";case"info":return"toast-info";case"error":default:return"toast-error"}};return i?s.jsx("div",{className:`toast ${h()} ${i?"toast-visible":"toast-hidden"}`,children:s.jsxs("div",{className:"toast-content",children:[s.jsxs("div",{className:"toast-header",children:[s.jsx("span",{className:"toast-icon",children:v()}),s.jsx("span",{className:"toast-message",children:e}),s.jsxs("div",{className:"toast-actions",children:[l&&a&&s.jsx("button",{onClick:()=>c(!u),className:"toast-details-btn","aria-label":u?"Ẩn chi tiết":"Hiện chi tiết",children:u?"▼":"▶"}),s.jsx("button",{onClick:f,className:"toast-close-btn","aria-label":"Đóng",children:"✕"})]})]}),u&&a&&s.jsx("div",{className:"toast-details",children:s.jsx("pre",{className:"toast-details-content",children:a})})]})}):null},Kl=({toasts:e,onRemoveToast:t})=>s.jsx("div",{className:"toast-container",children:e.map(n=>s.jsx(Rm,{message:n.message,type:n.type,duration:n.duration,showDetails:n.showDetails,details:n.details,onClose:()=>t(n.id)},n.id))}),kr=()=>{const[e,t]=g.useState([]),n=(c,f="error",v={})=>{const h=Date.now()+Math.random(),S={id:h,message:c,type:f,duration:v.duration||5e3,showDetails:v.showDetails||!1,details:v.details||null,...v};return t(N=>[...N,S]),h};return{toasts:e,addToast:n,removeToast:c=>{t(f=>f.filter(v=>v.id!==c))},clearToasts:()=>{t([])},showError:(c,f={})=>n(c,"error",f),showSuccess:(c,f={})=>n(c,"success",f),showWarning:(c,f={})=>n(c,"warning",f),showInfo:(c,f={})=>n(c,"info",f)}};function Tm(){const[e,t]=g.useState("scope"),[n,r]=g.useState(null),[l,a]=g.useState([]),[i,o]=g.useState("ALL_USERS"),[u,c]=g.useState(null),[f,v]=g.useState(!1),{toasts:h,removeToast:S,showError:N,showSuccess:_,showWarning:L}=kr(),p=async(E,P)=>{console.log("Scope selected:",E,P),c(null),v(!0),t("scanning");try{const j=await We("/api/scan/start",{userEmail:i,scope:E,...P});r(j),_("Bắt đầu quét thành công!"),d(j.sessionId)}catch(j){console.error("Error starting scan:",j);const C=Me(j);c(j),N(`Lỗi bắt đầu quét: ${C.message}`,{showDetails:!0,details:C.details,duration:8e3}),t("scope")}finally{v(!1)}},d=async E=>{const P=setInterval(async()=>{try{const j=await fe(`/api/scan/status/${E}`);if(r(j),j.status==="completed")clearInterval(P),_(`Quét hoàn thành! Tìm thấy ${j.total_files||0} file.`),t("files");else if(j.status==="failed"){clearInterval(P);const C=j.error_message||"Quét thất bại";c(new Error(C)),N(`Quét thất bại: ${C}`,{duration:8e3}),t("scope")}}catch(j){console.error("Error polling scan progress:",j),clearInterval(P);const C=Me(j);c(j),N(`Lỗi kiểm tra tiến trình quét: ${C.message}`,{showDetails:!0,details:C.details,duration:8e3}),t("scope")}},2e3)},m=E=>{a(E)},w=()=>{l.length>0&&t("migration")},x=async()=>{c(null),v(!0);try{const E=await We("/api/migration/start",{userEmail:i,scanSessionId:n.id,selectedFiles:l.map(P=>P.id)});console.log("Migration started:",E),_(`Bắt đầu migration ${l.length} file thành công!`)}catch(E){console.error("Error starting migration:",E);const P=Me(E);c(E),N(`Lỗi bắt đầu migration: ${P.message}`,{showDetails:!0,details:P.details,duration:8e3})}finally{v(!1)}};return s.jsxs("div",{className:"app",children:[s.jsxs("header",{className:"app-header",children:[s.jsx("h1",{children:"🚀 Drive-to-Lark Migrator"}),s.jsx("div",{className:"user-info",children:s.jsx(Fm,{value:i,onChange:E=>o(E),className:"user-email-selector",disabled:f})})]}),s.jsxs("main",{className:"app-main",children:[s.jsxs("div",{className:"step-indicator",children:[s.jsx("div",{className:`step ${e==="scope"?"active":e!=="scope"?"completed":""}`,children:"1. Select Scope"}),s.jsx("div",{className:`step ${e==="scanning"?"active":""}`,children:"2. Scanning"}),s.jsx("div",{className:`step ${e==="files"?"active":""}`,children:"3. Select Files"}),s.jsx("div",{className:`step ${e==="migration"?"active":""}`,children:"4. Migration"})]}),!1,u&&s.jsx(Yt,{error:u,title:"Lỗi trong quá trình xử lý",onDismiss:()=>c(null),onRetry:()=>{c(null)}}),s.jsxs("div",{className:"step-content",children:[e==="scope"&&s.jsx(_m,{onScopeSelected:p,userEmail:i,loading:f}),e==="scanning"&&s.jsx(Lm,{scanSession:n,onCancel:()=>t("scope")}),e==="files"&&s.jsx(Pm,{scanSession:n,onFilesSelected:m,selectedFiles:l,onProceedToMigration:w}),e==="migration"&&s.jsxs("div",{className:"migration-step",children:[s.jsx("h2",{children:"Ready to Migrate"}),s.jsxs("p",{children:["Selected ",l.length," files for migration"]}),s.jsx("button",{onClick:x,className:"btn btn-primary",disabled:l.length===0,children:"Start Migration"})]})]})]}),s.jsx(Kl,{toasts:h,onRemoveToast:S})]})}const Dm=({users:e,onSubmit:t,loading:n,initialData:r})=>{const[l,a]=g.useState({name:"",selectedUsers:[],downloadPath:"",concurrentDownloads:3,maxRetries:3,stopOnError:!0,continueOnError:!1,skipMimeTypes:"",processingOrder:"created_at"}),[i,o]=g.useState(!1),[u,c]=g.useState(""),[f,v]=g.useState("name"),[h,S]=g.useState({}),[N,_]=g.useState({});g.useEffect(()=>{r&&a({name:r.name||"",selectedUsers:r.selected_users||[],downloadPath:r.download_path||"",concurrentDownloads:r.concurrent_downloads||3,maxRetries:r.max_retries||3,stopOnError:r.stop_on_error!==void 0?r.stop_on_error:!0,continueOnError:r.continue_on_error!==void 0?r.continue_on_error:!1,skipMimeTypes:(r.skip_mime_types||[]).join(", "),processingOrder:r.processing_order||"created_at"})},[r]);const L=e.filter(j=>{const C=u.toLowerCase();return j.primary_email.toLowerCase().includes(C)||j.full_name&&j.full_name.toLowerCase().includes(C)}).sort((j,C)=>{switch(f){case"fileCount":return(C.fileCount||0)-(j.fileCount||0);case"totalSize":return(C.totalSize||0)-(j.totalSize||0);case"name":default:const y=(j.full_name||j.primary_email).toLowerCase(),k=(C.full_name||C.primary_email).toLowerCase();return y.localeCompare(k)}}),p=j=>{const{name:C,value:y}=j.target;a(k=>({...k,[C]:y})),h[C]&&S(k=>({...k,[C]:null}))},d=j=>{const{name:C,checked:y}=j.target;C==="stopOnError"?a(k=>({...k,stopOnError:y,continueOnError:!y})):C==="continueOnError"&&a(k=>({...k,continueOnError:y,stopOnError:!y}))},m=j=>{a(C=>{const y=C.selectedUsers.includes(j);let k;return y?k=C.selectedUsers.filter(T=>T!==j):k=[...C.selectedUsers,j],k.length===e.length?o(!0):o(!1),{...C,selectedUsers:k}}),h.selectedUsers&&S(C=>({...C,selectedUsers:null}))},w=()=>{const j=!i;o(j),a(j?C=>({...C,selectedUsers:e.map(y=>y.primary_email)}):C=>({...C,selectedUsers:[]})),h.selectedUsers&&S(C=>({...C,selectedUsers:null}))},x=async j=>{try{const C=await fe(`/api/download/users/${encodeURIComponent(j)}/undownloaded-files`);if(C.success){const y=C.data.length;_(k=>({...k,[j]:{...k[j],undownloadedCount:y}})),y>0&&!l.selectedUsers.includes(j)&&m(j)}}catch(C){console.error("Error getting undownloaded files:",C)}},E=j=>{j.preventDefault();const C={};if(l.name.trim()||(C.name="Session name is required"),l.selectedUsers.length===0&&(C.selectedUsers="Please select at least one user"),l.downloadPath.trim()||(C.downloadPath="Download path is required"),Object.keys(C).length>0){S(C);return}const y=l.skipMimeTypes.split(",").map(k=>k.trim()).filter(k=>k.length>0);t({...l,skipMimeTypes:y})},P=j=>{if(j===0)return"0 Bytes";const C=1024,y=["Bytes","KB","MB","GB","TB"],k=Math.floor(Math.log(j)/Math.log(C));return parseFloat((j/Math.pow(C,k)).toFixed(2))+" "+y[k]};return s.jsxs("div",{className:"download-config-form",children:[s.jsx("h2",{children:"Configure Download Session"}),s.jsxs("form",{onSubmit:E,children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"name",children:"Session Name"}),s.jsx("input",{type:"text",id:"name",name:"name",value:l.name,onChange:p,placeholder:"Enter a name for this download session",disabled:n}),h.name&&s.jsx("div",{className:"error-message",children:h.name})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"downloadPath",children:"Download Path"}),s.jsx("input",{type:"text",id:"downloadPath",name:"downloadPath",value:l.downloadPath,onChange:p,placeholder:"Enter local path to save files (e.g., /Users/<USER>/Downloads)",disabled:n}),h.downloadPath&&s.jsx("div",{className:"error-message",children:h.downloadPath}),s.jsx("div",{className:"help-text",children:"Files will be saved in this directory, organized by user email"})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"concurrentDownloads",children:"Concurrent Downloads"}),s.jsx("input",{type:"number",id:"concurrentDownloads",name:"concurrentDownloads",value:l.concurrentDownloads,onChange:p,min:"1",max:"10",disabled:n}),s.jsx("div",{className:"help-text",children:"Number of files to download simultaneously"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"maxRetries",children:"Max Retries"}),s.jsx("input",{type:"number",id:"maxRetries",name:"maxRetries",value:l.maxRetries,onChange:p,min:"0",max:"10",disabled:n}),s.jsx("div",{className:"help-text",children:"Number of times to retry failed downloads"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Error Handling"}),s.jsxs("div",{className:"error-handling-options",children:[s.jsx("div",{className:"radio-option",children:s.jsxs("label",{children:[s.jsx("input",{type:"radio",name:"stopOnError",checked:l.stopOnError,onChange:d,disabled:n}),s.jsxs("div",{className:"option-content",children:[s.jsx("div",{className:"option-title",children:"Stop on Error (Default)"}),s.jsx("div",{className:"option-description",children:"Stop the entire download process if any error occurs and log the error"})]})]})}),s.jsx("div",{className:"radio-option",children:s.jsxs("label",{children:[s.jsx("input",{type:"radio",name:"continueOnError",checked:l.continueOnError,onChange:d,disabled:n}),s.jsxs("div",{className:"option-content",children:[s.jsx("div",{className:"option-title",children:"Continue on Error"}),s.jsx("div",{className:"option-description",children:"Continue downloading other files if an error occurs and log errors for later review"})]})]})})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"skipMimeTypes",children:"Skip MIME Types (Optional)"}),s.jsx("input",{type:"text",id:"skipMimeTypes",name:"skipMimeTypes",value:l.skipMimeTypes,onChange:p,placeholder:"e.g., application/vnd.google-apps.shortcut, application/vnd.google-apps.folder",disabled:n}),s.jsxs("div",{className:"help-text",children:["Comma-separated list of MIME types to skip during migration. Common examples:",s.jsx("br",{}),"• application/vnd.google-apps.shortcut (shortcuts)",s.jsx("br",{}),"• application/vnd.google-apps.folder (folders)"]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"processingOrder",children:"Processing Order"}),s.jsxs("select",{id:"processingOrder",name:"processingOrder",value:l.processingOrder,onChange:p,disabled:n,children:[s.jsx("option",{value:"created_at",children:"Default (Creation Time)"}),s.jsx("option",{value:"user_email",children:"By User Email (Process all files for one user first)"}),s.jsx("option",{value:"size_asc",children:"By File Size (Smallest First)"}),s.jsx("option",{value:"size_desc",children:"By File Size (Largest First)"})]}),s.jsx("div",{className:"help-text",children:"Choose the order in which files will be processed during download"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Select Users to Download"}),h.selectedUsers&&s.jsx("div",{className:"error-message",children:h.selectedUsers}),s.jsxs("div",{className:"user-selection-header",children:[s.jsx("div",{className:"search-box",children:s.jsx("input",{type:"text",placeholder:"Search users...",value:u,onChange:j=>c(j.target.value),disabled:n})}),s.jsxs("div",{className:"sort-box",children:[s.jsx("label",{htmlFor:"sortBy",children:"Sort by:"}),s.jsxs("select",{id:"sortBy",value:f,onChange:j=>v(j.target.value),disabled:n,children:[s.jsx("option",{value:"name",children:"Name"}),s.jsx("option",{value:"fileCount",children:"Number of Files"}),s.jsx("option",{value:"totalSize",children:"Total File Size"})]})]}),s.jsx("div",{className:"select-all",children:s.jsxs("label",{children:[s.jsx("input",{type:"checkbox",checked:i,onChange:w,disabled:n}),"Select All Users"]})})]}),s.jsx("div",{className:"users-list",children:L.length===0?s.jsx("div",{className:"no-users",children:"No users found"}):L.map(j=>s.jsxs("div",{className:"user-item",children:[s.jsxs("label",{className:"user-checkbox",children:[s.jsx("input",{type:"checkbox",checked:l.selectedUsers.includes(j.primary_email),onChange:()=>m(j.primary_email),disabled:n}),s.jsxs("div",{className:"user-info-single-line",children:[s.jsx("span",{className:"user-name",children:j.full_name||j.primary_email}),s.jsxs("span",{className:"user-email",children:["(",j.primary_email,")"]}),s.jsxs("span",{className:"user-stats-inline",children:["• ",j.fileCount," files • ",P(j.totalSize)]})]})]}),s.jsx("button",{type:"button",className:"select-undownloaded-btn",onClick:()=>x(j.primary_email),disabled:n,title:"Select all undownloaded files for this user",children:"📥 Select Undownloaded"})]},j.user_id))})]}),s.jsx("div",{className:"form-actions",children:s.jsx("button",{type:"submit",className:"btn btn-primary",disabled:n,children:n?"Creating...":"Create Download Session"})})]})]})},zm=({session:e,onPause:t,onCancel:n,onComplete:r})=>{var C;const[l,a]=g.useState(null),[i,o]=g.useState([]),[u,c]=g.useState(!0),[f,v]=g.useState(1),[h]=g.useState(20),[S,N]=g.useState("all");g.useEffect(()=>{if(e!=null&&e.id){_(),L();const y=setInterval(()=>{_(),L()},2e3);return()=>clearInterval(y)}},[e==null?void 0:e.id,f,S]),g.useEffect(()=>{var y,k;(((y=l==null?void 0:l.session)==null?void 0:y.status)==="completed"||((k=l==null?void 0:l.session)==null?void 0:k.status)==="failed")&&(r==null||r())},[(C=l==null?void 0:l.session)==null?void 0:C.status,r]);const _=async()=>{try{const y=await fe(`/api/download/sessions/${e.id}/progress`);y.success&&a(y.data)}catch(y){console.error("Error loading progress:",y)}finally{c(!1)}},L=async()=>{try{const y=new URLSearchParams({page:f.toString(),limit:h.toString()});S!=="all"&&y.append("status",S);const k=await fe(`/api/download/sessions/${e.id}/items?${y}`);k.success&&o(k.data.items)}catch(y){console.error("Error loading download items:",y)}},p=y=>{if(y===0)return"0 Bytes";const k=1024,T=["Bytes","KB","MB","GB","TB"],z=Math.floor(Math.log(y)/Math.log(k));return parseFloat((y/Math.pow(k,z)).toFixed(2))+" "+T[z]},d=y=>{if(!y)return"-";const k=Math.floor(y/3600),T=Math.floor(y%3600/60),z=y%60;return k>0?`${k}h ${T}m ${z}s`:T>0?`${T}m ${z}s`:`${z}s`},m=y=>{switch(y){case"pending":return"⏳";case"downloading":return"📥";case"completed":return"✅";case"failed":return"❌";case"skipped":return"⏭️";default:return"❓"}},w=y=>{switch(y){case"pending":return"status-pending";case"downloading":return"status-downloading";case"completed":return"status-completed";case"failed":return"status-failed";case"skipped":return"status-skipped";default:return"status-unknown"}};if(u)return s.jsxs("div",{className:"download-progress loading",children:[s.jsx("div",{className:"loading-spinner"}),s.jsx("p",{children:"Loading progress..."})]});if(!l)return s.jsx("div",{className:"download-progress error",children:s.jsx("p",{children:"Failed to load progress data"})});const{session:x,progress:E}=l,P=x.progress_percentage||0,j=x.size_progress_percentage||0;return s.jsxs("div",{className:"download-progress",children:[s.jsxs("div",{className:"progress-header",children:[s.jsx("h2",{children:"Download Progress"}),s.jsx("div",{className:"session-status",children:s.jsx("span",{className:`status-badge ${w(x.status)}`,children:x.status})})]}),s.jsxs("div",{className:"overall-progress",children:[s.jsxs("div",{className:"progress-stats",children:[s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-value",children:x.downloaded_files||0}),s.jsx("div",{className:"stat-label",children:"Downloaded"})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-value",children:x.total_files||0}),s.jsx("div",{className:"stat-label",children:"Total Files"})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-value",children:p(x.downloaded_size||0)}),s.jsx("div",{className:"stat-label",children:"Downloaded Size"})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-value",children:p(x.total_size||0)}),s.jsx("div",{className:"stat-label",children:"Total Size"})]})]}),s.jsxs("div",{className:"progress-bars",children:[s.jsxs("div",{className:"progress-bar-container",children:[s.jsxs("div",{className:"progress-label",children:["Files Progress: ",P.toFixed(1),"%"]}),s.jsx("div",{className:"progress-bar",children:s.jsx("div",{className:"progress-fill",style:{width:`${P}%`}})})]}),s.jsxs("div",{className:"progress-bar-container",children:[s.jsxs("div",{className:"progress-label",children:["Size Progress: ",j.toFixed(1),"%"]}),s.jsx("div",{className:"progress-bar",children:s.jsx("div",{className:"progress-fill size-progress",style:{width:`${j}%`}})})]})]}),x.current_file_name&&s.jsxs("div",{className:"current-activity",children:[s.jsx("div",{className:"activity-icon",children:"📥"}),s.jsxs("div",{className:"activity-text",children:[s.jsx("div",{className:"current-user",children:x.current_user_email}),s.jsx("div",{className:"current-file",children:x.current_file_name})]})]}),s.jsx("div",{className:"session-duration",children:s.jsxs("span",{children:["Duration: ",d(x.duration_seconds)]})})]}),s.jsxs("div",{className:"status-summary",children:[s.jsxs("div",{className:"status-item",children:[s.jsx("span",{className:"status-icon",children:"⏳"}),s.jsx("span",{className:"status-count",children:E.pending||0}),s.jsx("span",{className:"status-text",children:"Pending"})]}),s.jsxs("div",{className:"status-item",children:[s.jsx("span",{className:"status-icon",children:"📥"}),s.jsx("span",{className:"status-count",children:E.downloading||0}),s.jsx("span",{className:"status-text",children:"Downloading"})]}),s.jsxs("div",{className:"status-item",children:[s.jsx("span",{className:"status-icon",children:"✅"}),s.jsx("span",{className:"status-count",children:E.completed||0}),s.jsx("span",{className:"status-text",children:"Completed"})]}),s.jsxs("div",{className:"status-item",children:[s.jsx("span",{className:"status-icon",children:"❌"}),s.jsx("span",{className:"status-count",children:E.failed||0}),s.jsx("span",{className:"status-text",children:"Failed"})]}),s.jsxs("div",{className:"status-item",children:[s.jsx("span",{className:"status-icon",children:"⏭️"}),s.jsx("span",{className:"status-count",children:E.skipped||0}),s.jsx("span",{className:"status-text",children:"Skipped"})]})]}),x.status==="running"&&s.jsxs("div",{className:"control-buttons",children:[s.jsx("button",{className:"btn btn-warning",onClick:t,children:"⏸️ Pause Download"}),s.jsx("button",{className:"btn btn-danger",onClick:n,children:"❌ Cancel Download"})]}),s.jsxs("div",{className:"download-items",children:[s.jsxs("div",{className:"items-header",children:[s.jsx("h3",{children:"Download Items"}),s.jsx("div",{className:"items-filter",children:s.jsxs("select",{value:S,onChange:y=>N(y.target.value),children:[s.jsx("option",{value:"all",children:"All Status"}),s.jsx("option",{value:"pending",children:"Pending"}),s.jsx("option",{value:"downloading",children:"Downloading"}),s.jsx("option",{value:"completed",children:"Completed"}),s.jsx("option",{value:"failed",children:"Failed"}),s.jsx("option",{value:"skipped",children:"Skipped"})]})})]}),s.jsx("div",{className:"items-list",children:i.map(y=>s.jsxs("div",{className:"download-item",children:[s.jsx("div",{className:"item-status",children:s.jsx("span",{className:`status-icon ${w(y.status)}`,children:m(y.status)})}),s.jsxs("div",{className:"item-info",children:[s.jsx("div",{className:"item-name",children:y.file_name}),s.jsx("div",{className:"item-path",children:y.file_path}),s.jsx("div",{className:"item-user",children:y.user_email})]}),s.jsxs("div",{className:"item-details",children:[s.jsx("div",{className:"item-size",children:p(y.file_size)}),y.error_message&&s.jsxs("div",{className:"item-error",title:y.error_message,children:["Error: ",y.error_message.substring(0,50),"..."]}),y.retry_count>0&&s.jsxs("div",{className:"item-retry",children:["Retries: ",y.retry_count]})]})]},y.id))}),i.length===0&&s.jsx("div",{className:"no-items",children:"No items found for the selected filter"})]})]})},Mm=({session:e,onStartNew:t})=>{const[n,r]=g.useState(null),[l,a]=g.useState([]),[i,o]=g.useState(!0),[u,c]=g.useState(!1),[f,v]=g.useState(!1);g.useEffect(()=>{e!=null&&e.id&&(h(),S())},[e==null?void 0:e.id]);const h=async()=>{try{const j=await fe(`/api/download/sessions/${e.id}/progress`);j.success&&r(j.data)}catch(j){console.error("Error loading report data:",j)}finally{o(!1)}},S=async()=>{try{const j=await fe(`/api/download/sessions/${e.id}/items?status=failed&limit=100`);j.success&&a(j.data.items)}catch(j){console.error("Error loading failed items:",j)}},N=()=>{const j={session:n.session,progress:n.progress,failedItems:l,exportedAt:new Date().toISOString()},C=JSON.stringify(j,null,2),y=new Blob([C],{type:"application/json"}),k=URL.createObjectURL(y),T=document.createElement("a");T.href=k,T.download=`download-report-${e.id}.json`,document.body.appendChild(T),T.click(),document.body.removeChild(T),URL.revokeObjectURL(k)},_=()=>{if(l.length===0){alert("No failed items to export");return}const C=[["File Name","File Path","User Email","Error Message","Retry Count"].join(","),...l.map(z=>[`"${z.file_name}"`,`"${z.file_path}"`,`"${z.user_email}"`,`"${z.error_message||""}"`,z.retry_count||0].join(","))].join(`
`),y=new Blob([C],{type:"text/csv"}),k=URL.createObjectURL(y),T=document.createElement("a");T.href=k,T.download=`failed-downloads-${e.id}.csv`,document.body.appendChild(T),T.click(),document.body.removeChild(T),URL.revokeObjectURL(k)},L=j=>{if(j===0)return"0 Bytes";const C=1024,y=["Bytes","KB","MB","GB","TB"],k=Math.floor(Math.log(j)/Math.log(C));return parseFloat((j/Math.pow(C,k)).toFixed(2))+" "+y[k]},p=j=>{if(!j)return"-";const C=Math.floor(j/3600),y=Math.floor(j%3600/60),k=j%60;return C>0?`${C}h ${y}m ${k}s`:y>0?`${y}m ${k}s`:`${k}s`},d=()=>{var y;if(!((y=n==null?void 0:n.session)!=null&&y.total_files))return 0;const j=n.progress.completed||0,C=n.session.total_files;return(j/C*100).toFixed(1)},m=j=>{switch(j){case"completed":return"#27ae60";case"failed":return"#e74c3c";case"cancelled":return"#95a5a6";default:return"#6c757d"}};if(i)return s.jsxs("div",{className:"download-report loading",children:[s.jsx("div",{className:"loading-spinner"}),s.jsx("p",{children:"Loading report..."})]});if(!n)return s.jsx("div",{className:"download-report error",children:s.jsx("p",{children:"Failed to load report data"})});const{session:w,progress:x}=n,E=w.status==="completed",P=(x.failed||0)>0;return s.jsxs("div",{className:"download-report",children:[s.jsxs("div",{className:"report-header",children:[s.jsx("h2",{children:"Download Report"}),s.jsxs("div",{className:"session-status",style:{color:m(w.status)},children:[w.status==="completed"?"✅":w.status==="failed"?"❌":"⚠️",w.status.toUpperCase()]})]}),s.jsxs("div",{className:"summary-cards",children:[s.jsxs("div",{className:"summary-card success",children:[s.jsx("div",{className:"card-icon",children:"✅"}),s.jsxs("div",{className:"card-content",children:[s.jsx("div",{className:"card-value",children:x.completed||0}),s.jsx("div",{className:"card-label",children:"Files Downloaded"})]})]}),s.jsxs("div",{className:"summary-card total",children:[s.jsx("div",{className:"card-icon",children:"📁"}),s.jsxs("div",{className:"card-content",children:[s.jsx("div",{className:"card-value",children:w.total_files||0}),s.jsx("div",{className:"card-label",children:"Total Files"})]})]}),s.jsxs("div",{className:"summary-card size",children:[s.jsx("div",{className:"card-icon",children:"💾"}),s.jsxs("div",{className:"card-content",children:[s.jsx("div",{className:"card-value",children:L(w.downloaded_size||0)}),s.jsx("div",{className:"card-label",children:"Downloaded Size"})]})]}),s.jsxs("div",{className:"summary-card rate",children:[s.jsx("div",{className:"card-icon",children:"📊"}),s.jsxs("div",{className:"card-content",children:[s.jsxs("div",{className:"card-value",children:[d(),"%"]}),s.jsx("div",{className:"card-label",children:"Success Rate"})]})]})]}),s.jsxs("div",{className:"session-details",children:[s.jsx("h3",{children:"Session Details"}),s.jsxs("div",{className:"details-grid",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("span",{className:"detail-label",children:"Session Name:"}),s.jsx("span",{className:"detail-value",children:w.name})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("span",{className:"detail-label",children:"Started:"}),s.jsx("span",{className:"detail-value",children:w.started_at?new Date(w.started_at).toLocaleString():"-"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("span",{className:"detail-label",children:"Completed:"}),s.jsx("span",{className:"detail-value",children:w.completed_at?new Date(w.completed_at).toLocaleString():"-"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("span",{className:"detail-label",children:"Duration:"}),s.jsx("span",{className:"detail-value",children:p(w.duration_seconds)})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("span",{className:"detail-label",children:"Download Path:"}),s.jsx("span",{className:"detail-value",children:w.download_path})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("span",{className:"detail-label",children:"Concurrent Downloads:"}),s.jsx("span",{className:"detail-value",children:w.concurrent_downloads})]})]})]}),s.jsxs("div",{className:"status-breakdown",children:[s.jsx("h3",{children:"Status Breakdown"}),s.jsxs("div",{className:"status-grid",children:[s.jsxs("div",{className:"status-item completed",children:[s.jsx("div",{className:"status-count",children:x.completed||0}),s.jsx("div",{className:"status-label",children:"Completed"})]}),s.jsxs("div",{className:"status-item failed",children:[s.jsx("div",{className:"status-count",children:x.failed||0}),s.jsx("div",{className:"status-label",children:"Failed"})]}),s.jsxs("div",{className:"status-item skipped",children:[s.jsx("div",{className:"status-count",children:x.skipped||0}),s.jsx("div",{className:"status-label",children:"Skipped"})]}),s.jsxs("div",{className:"status-item pending",children:[s.jsx("div",{className:"status-count",children:x.pending||0}),s.jsx("div",{className:"status-label",children:"Pending"})]})]})]}),P&&s.jsxs("div",{className:"failed-items-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsxs("h3",{children:["Failed Downloads (",l.length,")"]}),s.jsxs("button",{className:"btn btn-secondary",onClick:()=>c(!u),children:[u?"Hide":"Show"," Failed Items"]})]}),u&&s.jsx("div",{className:"failed-items-list",children:l.map(j=>s.jsxs("div",{className:"failed-item",children:[s.jsxs("div",{className:"item-info",children:[s.jsx("div",{className:"item-name",children:j.file_name}),s.jsx("div",{className:"item-path",children:j.file_path}),s.jsx("div",{className:"item-user",children:j.user_email})]}),s.jsxs("div",{className:"item-error",children:[s.jsx("div",{className:"error-message",children:j.error_message}),s.jsxs("div",{className:"retry-count",children:["Retries: ",j.retry_count||0]})]})]},j.id))})]}),w.continue_on_error&&w.error_log&&w.error_log.length>0&&s.jsxs("div",{className:"error-log-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsxs("h3",{children:["Error Log (",w.error_log.length," errors)"]}),s.jsxs("button",{className:"btn btn-secondary",onClick:()=>v(!f),children:[f?"Hide":"Show"," Error Log"]})]}),f&&s.jsx("div",{className:"error-log-list",children:w.error_log.map((j,C)=>s.jsxs("div",{className:"error-log-item",children:[s.jsx("div",{className:"error-timestamp",children:new Date(j.timestamp).toLocaleString()}),s.jsxs("div",{className:"error-details",children:[s.jsxs("div",{className:"error-file-info",children:[s.jsx("div",{className:"error-file-name",children:j.file_name}),s.jsx("div",{className:"error-file-path",children:j.file_path}),s.jsx("div",{className:"error-user-email",children:j.user_email})]}),s.jsxs("div",{className:"error-message-detail",children:[s.jsx("strong",{children:"Error:"})," ",j.error_message]}),s.jsxs("div",{className:"error-retry-info",children:["Retry attempt: ",j.retry_count||0]})]})]},C))})]}),s.jsxs("div",{className:"report-actions",children:[s.jsx("button",{className:"btn btn-primary",onClick:t,children:"🆕 Start New Download"}),s.jsx("button",{className:"btn btn-secondary",onClick:N,children:"📄 Export Report"}),P&&s.jsx("button",{className:"btn btn-warning",onClick:_,children:"📋 Export Failed Items"})]}),s.jsx("div",{className:`result-message ${E?"success":"failure"}`,children:E?s.jsxs("div",{children:[s.jsx("h4",{children:"🎉 Download Completed Successfully!"}),s.jsxs("p",{children:["All files have been downloaded to: ",s.jsx("strong",{children:w.download_path})]})]}):s.jsxs("div",{children:[s.jsx("h4",{children:"⚠️ Download Completed with Issues"}),s.jsxs("p",{children:[x.completed||0," files downloaded successfully,",x.failed||0," files failed. Check the failed items above for details."]})]})})]})},pd=g.createContext(),$m=({children:e})=>{const t=kr();return s.jsxs(pd.Provider,{value:t,children:[e,s.jsx(Kl,{toasts:t.toasts,onRemoveToast:t.removeToast})]})},Im=()=>{const e=g.useContext(pd);if(!e)throw new Error("useToast must be used within a ToastProvider");return e},Om=()=>{const[e,t]=g.useState("config"),[n,r]=g.useState([]),[l,a]=g.useState(null),[i,o]=g.useState(!1),[u,c]=g.useState([]),{showError:f,showSuccess:v,showWarning:h,showInfo:S}=Im();g.useEffect(()=>{N(),_()},[]);const N=async()=>{try{const y=await fe("/api/download/sessions");y.success&&r(y.data)}catch(y){console.error("Error loading sessions:",y),f("Failed to load download sessions")}},_=async()=>{try{const y=await fe("/api/download/users");y.success&&c(y.data)}catch(y){console.error("Error loading users:",y),f("Failed to load users")}},L=async y=>{try{o(!0);const k=await We("/api/download/sessions",y);k.success?(a(k.data),t("progress"),await N(),v("Download session created successfully")):f(k.error||"Failed to create download session")}catch(k){console.error("Error creating session:",k),f("Failed to create download session")}finally{o(!1)}},p=async y=>{try{o(!0);const k=await We(`/api/download/sessions/${y}/start`);k.success?(await N(),v("Download started successfully")):f(k.error||"Failed to start download")}catch(k){console.error("Error starting session:",k),f("Failed to start download")}finally{o(!1)}},d=async y=>{try{const k=await We(`/api/download/sessions/${y}/pause`);k.success?(await N(),S("Download paused")):f(k.error||"Failed to pause download")}catch(k){console.error("Error pausing session:",k),f("Failed to pause download")}},m=async y=>{try{const k=await We(`/api/download/sessions/${y}/cancel`);k.success?(await N(),h("Download cancelled")):f(k.error||"Failed to cancel download")}catch(k){console.error("Error cancelling session:",k),f("Failed to cancel download")}},w=async y=>{if(window.confirm("Are you sure you want to delete this session?"))try{const k=await Cm(`/api/download/sessions/${y}`);k.success?(await N(),v("Session deleted successfully")):f(k.error||"Failed to delete session")}catch(k){console.error("Error deleting session:",k),f("Failed to delete session")}},x=y=>{a(y),y.status==="running"?t("progress"):y.status==="completed"||y.status==="failed"?t("report"):t("config")},E=()=>{a(null),t("config"),N()},P=y=>{if(y===0)return"0 Bytes";const k=1024,T=["Bytes","KB","MB","GB","TB"],z=Math.floor(Math.log(y)/Math.log(k));return parseFloat((y/Math.pow(k,z)).toFixed(2))+" "+T[z]},j=y=>{if(!y)return"-";const k=Math.floor(y/3600),T=Math.floor(y%3600/60),z=y%60;return k>0?`${k}h ${T}m ${z}s`:T>0?`${T}m ${z}s`:`${z}s`},C=y=>{switch(y){case"pending":return"status-pending";case"running":return"status-running";case"paused":return"status-paused";case"completed":return"status-completed";case"failed":return"status-failed";case"cancelled":return"status-cancelled";default:return"status-unknown"}};return s.jsxs("div",{className:"download-page",children:[s.jsxs("div",{className:"page-header",children:[s.jsx("h1",{children:"📥 Download Files from Google Drive"}),s.jsx("p",{children:"Download files from Google Drive to local storage with progress tracking"})]}),l?s.jsxs("div",{className:"session-detail",children:[s.jsxs("div",{className:"session-nav",children:[s.jsx("button",{className:"btn btn-secondary",onClick:E,children:"← Back to Sessions"}),s.jsx("h2",{children:l.name})]}),e==="config"&&s.jsx(Dm,{users:u,onSubmit:L,loading:i,initialData:l}),e==="progress"&&s.jsx(zm,{session:l,onPause:()=>d(l.id),onCancel:()=>m(l.id),onComplete:()=>t("report")}),e==="report"&&s.jsx(Mm,{session:l,onStartNew:()=>t("config")})]}):s.jsxs("div",{className:"sessions-overview",children:[s.jsxs("div",{className:"actions-bar",children:[s.jsx("button",{className:"btn btn-primary",onClick:()=>{a({name:"",status:"new"}),t("config")},children:"➕ Create New Download Session"}),s.jsx("button",{className:"btn btn-secondary",onClick:N,children:"🔄 Refresh"})]}),s.jsxs("div",{className:"sessions-list",children:[s.jsx("h2",{children:"Download Sessions"}),n.length===0?s.jsx("div",{className:"empty-state",children:s.jsx("p",{children:"No download sessions found. Create your first session to get started."})}):s.jsx("div",{className:"sessions-grid",children:n.map(y=>s.jsxs("div",{className:"session-card",children:[s.jsxs("div",{className:"session-header",children:[s.jsx("h3",{children:y.name}),s.jsx("span",{className:`status-badge ${C(y.status)}`,children:y.status})]}),s.jsxs("div",{className:"session-stats",children:[s.jsxs("div",{className:"stat",children:[s.jsx("span",{className:"label",children:"Files:"}),s.jsxs("span",{className:"value",children:[y.downloaded_files||0," / ",y.total_files||0]})]}),s.jsxs("div",{className:"stat",children:[s.jsx("span",{className:"label",children:"Size:"}),s.jsxs("span",{className:"value",children:[P(y.downloaded_size||0)," / ",P(y.total_size||0)]})]}),s.jsxs("div",{className:"stat",children:[s.jsx("span",{className:"label",children:"Progress:"}),s.jsxs("span",{className:"value",children:[y.progress_percentage||0,"%"]})]}),s.jsxs("div",{className:"stat",children:[s.jsx("span",{className:"label",children:"Duration:"}),s.jsx("span",{className:"value",children:j(y.duration_seconds)})]})]}),s.jsxs("div",{className:"session-actions",children:[s.jsx("button",{className:"btn btn-sm btn-primary",onClick:()=>x(y),children:"👁️ View"}),y.status==="pending"&&s.jsx("button",{className:"btn btn-sm btn-success",onClick:()=>p(y.id),disabled:i,children:"▶️ Start"}),y.status==="running"&&s.jsx("button",{className:"btn btn-sm btn-warning",onClick:()=>d(y.id),children:"⏸️ Pause"}),(y.status==="running"||y.status==="paused")&&s.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>m(y.id),children:"❌ Cancel"}),(y.status==="completed"||y.status==="failed"||y.status==="cancelled")&&s.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>w(y.id),children:"🗑️ Delete"})]})]},y.id))})]})]})]})};function Um({users:e,selectedUser:t,onUserSelect:n,loading:r,viewMode:l="grid"}){if(r)return s.jsxs("div",{className:"user-list-loading",children:[s.jsx("div",{className:"loading-spinner"}),s.jsx("p",{children:"Đang tải danh sách người dùng..."})]});if(e.length===0)return s.jsxs("div",{className:"user-list-empty",children:[s.jsx("div",{className:"empty-icon",children:"👥"}),s.jsx("p",{children:"Không tìm thấy người dùng nào"})]});const a=(c,f)=>f?f.split(" ").map(v=>v[0]).join("").toUpperCase().slice(0,2):c.split("@")[0].slice(0,2).toUpperCase(),i=c=>c.name||c.email.split("@")[0],o=c=>({files:c.total_files||0,size:u(c.total_size||0),lastActive:c.last_active?new Date(c.last_active).toLocaleDateString("vi-VN"):"Chưa rõ"}),u=c=>{if(c===0)return"0 B";const f=1024,v=["B","KB","MB","GB","TB"],h=Math.floor(Math.log(c)/Math.log(f));return parseFloat((c/Math.pow(f,h)).toFixed(1))+" "+v[h]};return s.jsx("div",{className:`user-list ${l}`,children:e.map(c=>{const f=(t==null?void 0:t.email)===c.email,v=o(c),h=a(c.email,c.name),S=i(c);return s.jsxs("div",{className:`user-item ${f?"selected":""}`,onClick:()=>n(c),role:"button",tabIndex:0,onKeyDown:N=>{(N.key==="Enter"||N.key===" ")&&n(c)},children:[s.jsxs("div",{className:"user-avatar",children:[c.avatar?s.jsx("img",{src:c.avatar,alt:S}):s.jsx("div",{className:"user-initials",children:h}),f&&s.jsx("div",{className:"selection-indicator",children:"✓"})]}),s.jsxs("div",{className:"user-info",children:[s.jsxs("div",{className:"user-primary",children:[s.jsx("div",{className:"user-name",title:S,children:S}),s.jsx("div",{className:"user-email",title:c.email,children:c.email})]}),s.jsxs("div",{className:"user-stats",children:[s.jsxs("div",{className:"stat-item",children:[s.jsx("span",{className:"stat-icon",children:"📁"}),s.jsx("span",{className:"stat-value",children:v.files})]}),s.jsxs("div",{className:"stat-item",children:[s.jsx("span",{className:"stat-icon",children:"💾"}),s.jsx("span",{className:"stat-value",children:v.size})]}),l==="list"&&s.jsxs("div",{className:"stat-item",children:[s.jsx("span",{className:"stat-icon",children:"📅"}),s.jsx("span",{className:"stat-value",children:v.lastActive})]})]}),c.status&&s.jsxs("div",{className:`user-status status-${c.status}`,children:[c.status==="active"&&"🟢 Hoạt động",c.status==="inactive"&&"🟡 Không hoạt động",c.status==="blocked"&&"🔴 Bị chặn"]})]}),l==="grid"&&s.jsx("div",{className:"user-actions",children:s.jsx("button",{className:"btn-action",onClick:N=>{N.stopPropagation(),console.log("Quick action for",c.email)},title:"Hành động nhanh",children:"⚡"})})]},c.email)})})}function Bm({value:e,onChange:t,placeholder:n="Tìm kiếm...",onFilter:r}){const[l,a]=g.useState(!1),[i,o]=g.useState(!1),u=g.useRef(null),c=()=>{var v;t(""),(v=u.current)==null||v.focus()},f=v=>{v.key==="Escape"&&c()};return g.useEffect(()=>{const v=h=>{u.current&&!u.current.contains(h.target)&&o(!1)};return document.addEventListener("mousedown",v),()=>document.removeEventListener("mousedown",v)},[]),s.jsxs("div",{className:`user-search ${l?"focused":""}`,children:[s.jsxs("div",{className:"search-input-container",children:[s.jsx("div",{className:"search-icon",children:"🔍"}),s.jsx("input",{ref:u,type:"text",value:e,onChange:v=>t(v.target.value),onFocus:()=>a(!0),onBlur:()=>a(!1),onKeyDown:f,placeholder:n,className:"search-input"}),e&&s.jsx("button",{className:"clear-button",onClick:c,title:"Xóa tìm kiếm",children:"✕"}),r&&s.jsx("button",{className:`filter-button ${i?"active":""}`,onClick:()=>o(!i),title:"Bộ lọc",children:"⚙️"})]}),i&&r&&s.jsxs("div",{className:"search-filters",children:[s.jsxs("div",{className:"filter-group",children:[s.jsx("label",{className:"filter-label",children:"Trạng thái:"}),s.jsxs("div",{className:"filter-options",children:[s.jsxs("label",{className:"filter-option",children:[s.jsx("input",{type:"checkbox"})," Hoạt động"]}),s.jsxs("label",{className:"filter-option",children:[s.jsx("input",{type:"checkbox"})," Không hoạt động"]}),s.jsxs("label",{className:"filter-option",children:[s.jsx("input",{type:"checkbox"})," Bị chặn"]})]})]}),s.jsxs("div",{className:"filter-group",children:[s.jsx("label",{className:"filter-label",children:"Kích thước dữ liệu:"}),s.jsx("div",{className:"filter-range",children:s.jsxs("select",{className:"filter-select",children:[s.jsx("option",{value:"",children:"Tất cả"}),s.jsx("option",{value:"small",children:"Dưới 1GB"}),s.jsx("option",{value:"medium",children:"1GB - 10GB"}),s.jsx("option",{value:"large",children:"Trên 10GB"})]})})]}),s.jsxs("div",{className:"filter-actions",children:[s.jsx("button",{className:"btn-filter btn-apply",children:"Áp dụng"}),s.jsx("button",{className:"btn-filter btn-reset",children:"Đặt lại"})]})]})]})}function Am({users:e,selectedUser:t,userFiles:n}){const r=()=>{const i=e.length,o=e.filter(f=>f.status==="active").length,u=e.reduce((f,v)=>f+(v.total_files||0),0),c=e.reduce((f,v)=>f+(v.total_size||0),0);return{totalUsers:i,activeUsers:o,totalFiles:u,totalSize:c,selectedUserFiles:n.length,selectedUserSize:n.reduce((f,v)=>f+(v.size||0),0)}},l=i=>{if(i===0)return"0 B";const o=1024,u=["B","KB","MB","GB","TB"],c=Math.floor(Math.log(i)/Math.log(o));return parseFloat((i/Math.pow(o,c)).toFixed(1))+" "+u[c]},a=r();return s.jsxs("div",{className:"user-stats",children:[s.jsxs("div",{className:"stats-section global-stats",children:[s.jsx("h3",{children:"📊 Thống kê tổng quan"}),s.jsxs("div",{className:"stats-grid",children:[s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"👥"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-value",children:a.totalUsers}),s.jsx("div",{className:"stat-label",children:"Tổng người dùng"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"🟢"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-value",children:a.activeUsers}),s.jsx("div",{className:"stat-label",children:"Đang hoạt động"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"📁"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-value",children:a.totalFiles.toLocaleString()}),s.jsx("div",{className:"stat-label",children:"Tổng files"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"💾"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("div",{className:"stat-value",children:l(a.totalSize)}),s.jsx("div",{className:"stat-label",children:"Tổng dung lượng"})]})]})]})]}),t&&s.jsxs("div",{className:"stats-section selected-stats",children:[s.jsx("h3",{children:"🎯 Thông tin được chọn"}),s.jsxs("div",{className:"selected-user-info",children:[s.jsxs("div",{className:"selected-user-header",children:[s.jsx("div",{className:"selected-user-avatar",children:t.avatar?s.jsx("img",{src:t.avatar,alt:t.name||t.email}):s.jsx("div",{className:"selected-user-initials",children:(t.name||t.email).slice(0,2).toUpperCase()})}),s.jsxs("div",{className:"selected-user-details",children:[s.jsx("div",{className:"selected-user-name",children:t.name||t.email.split("@")[0]}),s.jsx("div",{className:"selected-user-email",children:t.email})]})]}),s.jsxs("div",{className:"selected-stats-grid",children:[s.jsxs("div",{className:"selected-stat",children:[s.jsx("span",{className:"selected-stat-icon",children:"📁"}),s.jsx("span",{className:"selected-stat-value",children:a.selectedUserFiles}),s.jsx("span",{className:"selected-stat-label",children:"files đã tải"})]}),s.jsxs("div",{className:"selected-stat",children:[s.jsx("span",{className:"selected-stat-icon",children:"💾"}),s.jsx("span",{className:"selected-stat-value",children:l(a.selectedUserSize)}),s.jsx("span",{className:"selected-stat-label",children:"dung lượng"})]}),s.jsxs("div",{className:"selected-stat",children:[s.jsx("span",{className:"selected-stat-icon",children:"📂"}),s.jsx("span",{className:"selected-stat-value",children:n.filter(i=>i.mimeType==="application/vnd.google-apps.folder").length}),s.jsx("span",{className:"selected-stat-label",children:"thư mục"})]})]})]})]})]})}function Hm(){const[e,t]=g.useState([]),[n,r]=g.useState(null),[l,a]=g.useState([]),[i,o]=g.useState(null),[u,c]=g.useState([]),[f,v]=g.useState(!1),[h,S]=g.useState(!1),[N,_]=g.useState(null),[L,p]=g.useState(""),[d,m]=g.useState("grid"),[w,x]=g.useState(!1),{toasts:E,removeToast:P,showError:j,showSuccess:C,showWarning:y}=kr();g.useEffect(()=>{k()},[]);const k=async()=>{var Q;v(!0),_(null);try{const R=await fe("/api/scan/users");t(R.users||[]),C(`Đã tải ${((Q=R.users)==null?void 0:Q.length)||0} người dùng`)}catch(R){console.error("Error loading users:",R);const $=Me(R);_(R),j(`Lỗi tải danh sách người dùng: ${$.message}`,{showDetails:!0,details:$.details,duration:8e3})}finally{v(!1)}},T=async Q=>{S(!0),_(null);try{const R=await fe(`/api/scan/users/${Q.email}/files`);a(Array.isArray(R.tree)?R.tree:[]),o(R.stats||null),r(Q),c([]),C(`Đã tải cấu trúc thư mục của ${Q.email}`)}catch(R){console.error("Error loading user files:",R);const $=Me(R);_(R),j(`Lỗi tải thư mục của ${Q.email}: ${$.message}`,{showDetails:!0,details:$.details,duration:8e3}),a([]),o(null),c([])}finally{S(!1)}},z=Q=>{(n==null?void 0:n.email)===Q.email?(r(null),setUserFiles([])):T(Q)},B=()=>{k()},Mt=()=>{n&&T(n)},pt=e.filter(Q=>{var R;return Q.email.toLowerCase().includes(L.toLowerCase())||((R=Q.name)==null?void 0:R.toLowerCase().includes(L.toLowerCase()))});return s.jsxs("div",{className:"users-overview",children:[s.jsxs("header",{className:"users-overview-header",children:[s.jsxs("div",{className:"header-content",children:[s.jsx("h1",{children:"📁 Quản lý Users & Files"}),s.jsxs("div",{className:"header-actions",children:[s.jsx(Bm,{value:L,onChange:p,placeholder:"Tìm kiếm theo email hoặc tên..."}),s.jsxs("div",{className:"view-controls",children:[s.jsx("button",{className:`btn-icon ${d==="grid"?"active":""}`,onClick:()=>m("grid"),title:"Chế độ lưới",children:"⊞"}),s.jsx("button",{className:`btn-icon ${d==="list"?"active":""}`,onClick:()=>m("list"),title:"Chế độ danh sách",children:"☰"}),s.jsx("button",{className:"btn-icon",onClick:()=>x(!w),title:w?"Mở rộng sidebar":"Thu gọn sidebar",children:w?"▶":"◀"})]}),s.jsx("button",{className:"btn btn-secondary",onClick:B,disabled:f,children:"🔄 Làm mới"})]})]}),s.jsx(Am,{users:e,selectedUser:n,userFiles:l})]}),s.jsxs("main",{className:`users-overview-main ${w?"sidebar-collapsed":""}`,children:[s.jsxs("aside",{className:"users-sidebar",children:[s.jsxs("div",{className:"sidebar-header",children:[s.jsxs("h2",{children:["👥 Người dùng (",pt.length,")"]}),n&&s.jsx("button",{className:"btn-small btn-ghost",onClick:()=>{r(null),setUserFiles([])},title:"Bỏ chọn user",children:"✕"})]}),N&&s.jsx(Yt,{error:N,title:"Lỗi tải dữ liệu",onDismiss:()=>_(null),onRetry:()=>{n?T(n):k()},compact:!0}),s.jsx(Um,{users:pt,selectedUser:n,onUserSelect:z,loading:f,viewMode:d})]}),s.jsx("section",{className:"file-tree-content",children:n?s.jsxs("div",{className:"file-tree-container",children:[s.jsxs("div",{className:"file-tree-header",children:[s.jsxs("h2",{children:["📂 Thư mục của ",n.email]}),s.jsxs("div",{className:"file-tree-actions",children:[s.jsxs("span",{className:"file-count",children:[Array.isArray(l)?l.length:0," mục"]}),s.jsx("button",{className:"btn-small btn-secondary",onClick:Mt,disabled:h,children:"🔄 Làm mới"})]})]}),s.jsx(hd,{stats:i,selectedFiles:u}),s.jsx(fd,{tree:Array.isArray(l)?l:[],selectedFiles:u,onFileSelect:(Q,R)=>{let $;R?$=[...u,Q]:$=u.filter(O=>O.id!==Q.id),c($)},onSelectAll:Q=>c(Q)})]}):s.jsx("div",{className:"empty-state",children:s.jsxs("div",{className:"empty-state-content",children:[s.jsx("div",{className:"empty-icon",children:"👆"}),s.jsx("h3",{children:"Chọn một người dùng"}),s.jsx("p",{children:"Chọn một người dùng từ danh sách bên trái để xem cấu trúc thư mục của họ"}),e.length===0&&!f&&s.jsxs("div",{className:"empty-users",children:[s.jsx("p",{children:"Không có người dùng nào. Hãy kiểm tra kết nối API."}),s.jsx("button",{className:"btn btn-primary",onClick:B,children:"Thử lại"})]})]})})})]}),s.jsx(Kl,{toasts:E,onRemoveToast:P})]})}function Wm(){const[e,t]=g.useState([]),[n,r]=g.useState([]),[l,a]=g.useState(!1),[i,o]=g.useState(!1),[u,c]=g.useState(!1),[f,v]=g.useState(null),[h,S]=g.useState(null),{toasts:N,removeToast:_,showError:L,showSuccess:p,showWarning:d}=kr();g.useEffect(()=>{m()},[]);const m=async(k=!1)=>{var T;o(!0),v(null);try{const B=await fe(k?"/api/scan/users?forceScan=true":"/api/scan/users");t(B.users||[]),p(`Tải thành công ${((T=B.users)==null?void 0:T.length)||0} người dùng`)}catch(z){console.error("Error fetching users:",z);const B=Me(z);v(z),L(`Lỗi tải danh sách người dùng: ${B.message}`,{showDetails:!0,details:B.details,duration:8e3})}finally{o(!1)}},w=k=>{r(T=>T.some(B=>B.userId===k.userId)?T.filter(B=>B.userId!==k.userId):[...T,k])},x=()=>{n.length===e.length?r([]):r([...e])},E=async()=>{if(n.length===0){d("Vui lòng chọn ít nhất một người dùng để scan");return}c(!0),v(null);try{const k=n.map(z=>z.email),T=await We("/api/scan/start",{scope:"selected_users",userEmails:k,forceScan:l});S(T),p(`Bắt đầu scan thành công cho ${n.length} người dùng!`),P(T.sessionId)}catch(k){console.error("Error starting scan:",k);const T=Me(k);v(k),L(`Lỗi bắt đầu scan: ${T.message}`,{showDetails:!0,details:T.details,duration:8e3})}finally{c(!1)}},P=async k=>{const T=setInterval(async()=>{try{const z=await fe(`/api/scan/status/${k}`);if(S(z),z.status==="completed")clearInterval(T),p(`Scan hoàn thành! Tìm thấy ${z.total_files||0} file.`);else if(z.status==="failed"){clearInterval(T);const B=z.error_message||"Scan thất bại";v(new Error(B)),L(`Scan thất bại: ${B}`,{duration:8e3})}}catch(z){console.error("Error polling scan progress:",z),clearInterval(T);const B=Me(z);v(z),L(`Lỗi kiểm tra tiến trình scan: ${B.message}`,{showDetails:!0,details:B.details,duration:8e3})}},2e3)},j=k=>k?new Date(k).toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"Chưa có thông tin",C=k=>{if(k===0)return"0 Bytes";const T=1024,z=["Bytes","KB","MB","GB","TB"],B=Math.floor(Math.log(k)/Math.log(T));return parseFloat((k/Math.pow(T,B)).toFixed(2))+" "+z[B]},y=k=>k?k.split(" ").map(T=>T.charAt(0)).join("").toUpperCase().slice(0,2):"??";return s.jsxs("div",{className:"app",children:[s.jsxs("header",{className:"app-header",children:[s.jsx("h1",{children:"👥 User Management & Scan"}),s.jsx("p",{children:"Quản lý người dùng và thực hiện scan files từ Google Workspace"})]}),s.jsxs("main",{className:"app-main",children:[f&&s.jsx(Yt,{error:f,title:"Lỗi trong quá trình xử lý",onDismiss:()=>v(null),onRetry:()=>{v(null),m(l)}}),e.length>0&&s.jsxs("div",{className:"stats-overview",children:[s.jsxs("div",{className:"stats-card primary",children:[s.jsx("div",{className:"stat-number",children:e.length}),s.jsx("div",{className:"stat-label",children:"Tổng số người dùng"})]}),s.jsxs("div",{className:"stats-card success",children:[s.jsx("div",{className:"stat-number",children:e.filter(k=>!k.suspended).length}),s.jsx("div",{className:"stat-label",children:"Đang hoạt động"})]}),s.jsxs("div",{className:"stats-card warning",children:[s.jsx("div",{className:"stat-number",children:e.filter(k=>k.suspended).length}),s.jsx("div",{className:"stat-label",children:"Bị khóa"})]}),s.jsxs("div",{className:"stats-card",children:[s.jsx("div",{className:"stat-number",children:n.length}),s.jsx("div",{className:"stat-label",children:"Đã chọn"})]})]}),s.jsxs("div",{className:"controls-section",children:[s.jsx("h2",{children:"Cài đặt & Điều khiển"}),s.jsxs("div",{className:"force-scan-option",children:[s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",checked:l,onChange:k=>a(k.target.checked),disabled:i||u}),s.jsx("span",{className:"checkbox-custom"}),s.jsx("span",{children:"Force Scan - Quét lại danh sách người dùng từ Google Workspace"})]}),s.jsx("div",{className:"force-scan-description",children:"Khi bật tùy chọn này, hệ thống sẽ quét lại toàn bộ danh sách người dùng từ Google Workspace thay vì sử dụng dữ liệu đã cache. Điều này đảm bảo dữ liệu luôn được cập nhật mới nhất nhưng có thể mất nhiều thời gian hơn."})]}),s.jsxs("div",{className:"action-buttons",children:[s.jsx("button",{onClick:()=>m(l),className:"btn btn-secondary",disabled:i||u,children:i?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"0.5rem"}}),"Đang tải..."]}):"🔄 Tải danh sách người dùng"}),s.jsx("button",{onClick:E,className:"btn btn-primary btn-large",disabled:n.length===0||u,children:u?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"0.5rem"}}),"Đang bắt đầu scan..."]}):`🚀 Scan files (${n.length} người dùng)`})]})]}),h&&s.jsxs("div",{className:"scan-progress-mini",children:[s.jsx("h4",{children:"📊 Tiến trình Scan"}),h.status==="running"&&s.jsxs("div",{className:"progress-bar-container",children:[s.jsx("div",{className:"progress-bar",children:s.jsx("div",{className:"progress-fill",style:{width:`${Math.min(100,(h.processed_files||0)/Math.max(1,h.total_files||1)*100)}%`}})}),s.jsxs("div",{className:"progress-text",children:[h.processed_files||0," /"," ",h.total_files||0," files"]})]}),s.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"1rem",marginTop:"1rem"},children:[s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Trạng thái:"}),s.jsxs("span",{className:`detail-value status-${h.status}`,children:[h.status==="running"&&"🔄 Đang chạy",h.status==="completed"&&"✅ Hoàn thành",h.status==="failed"&&"❌ Thất bại",h.status==="cancelled"&&"⏹️ Đã hủy"]})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Files:"}),s.jsx("span",{className:"detail-value",children:h.total_files||0})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Folders:"}),s.jsx("span",{className:"detail-value",children:h.total_folders||0})]}),h.status==="completed"&&s.jsxs("div",{className:"detail-row",children:[s.jsx("span",{className:"detail-label",children:"Kích thước:"}),s.jsx("span",{className:"detail-value",children:C(h.total_size||0)})]})]}),h.error_message&&s.jsxs("div",{style:{marginTop:"1rem",padding:"0.75rem",background:"#fee2e2",borderRadius:"6px",borderLeft:"3px solid #dc2626"},children:[s.jsx("strong",{style:{color:"#991b1b"},children:"Lỗi:"}),s.jsx("span",{style:{color:"#7f1d1d",marginLeft:"0.5rem"},children:h.error_message})]}),h.status==="completed"&&s.jsxs("div",{className:"scan-success",children:[s.jsx("h4",{children:"🎉 Scan hoàn thành thành công!"}),s.jsxs("p",{children:["Đã quét thành công ",h.total_files||0," files từ"," ",n.length," người dùng."]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"selection-summary",children:[s.jsxs("div",{className:"selection-info",children:[s.jsxs("span",{children:["Tổng số người dùng: ",e.length]}),s.jsxs("span",{children:["Đã chọn: ",n.length]})]}),s.jsxs("label",{className:"select-all-checkbox",children:[s.jsx("input",{type:"checkbox",checked:n.length===e.length&&e.length>0,onChange:x,disabled:i||u}),s.jsx("span",{className:"checkbox-custom"}),s.jsx("span",{children:"Chọn tất cả"})]})]}),i?s.jsxs("div",{className:"loading-state",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Đang tải danh sách người dùng..."})]}):e.length===0?s.jsx("div",{className:"empty-state",children:s.jsx("p",{children:"Không có người dùng nào. Vui lòng thử tải lại với Force Scan."})}):s.jsxs("div",{className:"users-table",children:[s.jsxs("div",{className:"table-header",children:[s.jsx("div",{children:"Chọn"}),s.jsx("div",{children:"Người dùng"}),s.jsx("div",{children:"Email"}),s.jsx("div",{children:"Trạng thái"}),s.jsx("div",{children:"Đăng nhập cuối"}),s.jsx("div",{children:"Ngày tạo"})]}),s.jsx("div",{className:"table-body",children:e.map(k=>s.jsxs("div",{className:`table-row ${n.some(T=>T.userId===k.userId)?"selected":""}`,children:[s.jsx("div",{className:"col-select",children:s.jsxs("label",{className:"file-checkbox",children:[s.jsx("input",{type:"checkbox",checked:n.some(T=>T.userId===k.userId),onChange:()=>w(k),disabled:i||u}),s.jsx("span",{className:"checkbox-custom"})]})}),s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.75rem"},children:[s.jsx("div",{className:"user-avatar",style:{width:"40px",height:"40px",borderRadius:"50%",background:k.suspended?"linear-gradient(135deg, #ef4444, #dc2626)":"linear-gradient(135deg, #3b82f6, #1d4ed8)",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"0.875rem",fontWeight:"600",flexShrink:0},children:y(k.fullName)}),s.jsxs("div",{children:[s.jsx("div",{className:"file-name",children:k.fullName||"Chưa có tên"}),s.jsxs("div",{className:"file-path",children:[k.givenName," ",k.familyName]})]})]}),s.jsxs("div",{children:[s.jsx("div",{className:"file-name",children:k.email}),s.jsxs("div",{className:"file-path",children:["ID: ",k.userId]})]}),s.jsx("div",{children:s.jsx("span",{className:`user-status ${k.suspended?"suspended":"active"}`,style:{padding:"0.25rem 0.75rem",borderRadius:"20px",fontSize:"0.75rem",fontWeight:"500",background:k.suspended?"#fee2e2":"#dcfce7",color:k.suspended?"#991b1b":"#166534"},children:k.suspended?"🔒 Bị khóa":"✅ Hoạt động"})}),s.jsx("div",{children:s.jsx("div",{style:{fontSize:"0.875rem"},children:j(k.lastLoginTime)})}),s.jsx("div",{children:s.jsx("div",{style:{fontSize:"0.875rem"},children:j(k.createdAt)})})]},k.userId))})]})]})]}),s.jsx(Kl,{toasts:N,onRemoveToast:_})]})}function Vm(){const[e,t]=g.useState([]),[n,r]=g.useState(!1),[l,a]=g.useState(null),[i,o]=g.useState(null),[u,c]=g.useState(""),[f,v]=g.useState("total_usage_bytes"),[h,S]=g.useState("desc"),[N,_]=g.useState(1),[L,p]=g.useState(50),[d,m]=g.useState(null),[w,x]=g.useState("E:\\"),[E,P]=g.useState(new Set),[j,C]=g.useState(!1),[y,k]=g.useState(!1),{showError:T,showSuccess:z,showWarning:B}=kr();g.useEffect(()=>{Mt()},[u,f,h,N,L]);const Mt=async()=>{r(!0),a(null);try{const D=new URLSearchParams({search:u,sortBy:f,sortOrder:h,page:N.toString(),pageSize:L.toString()}),I=await fe(`/api/storage/stats?${D}`);t(I.users),m(I.pagination),o(I.overallStats)}catch(D){console.error("Error loading storage data:",D);const I=Me(D);a(D),T(`Lỗi tải dữ liệu: ${I.message}`,{showDetails:!0,details:I.details})}finally{r(!1)}},pt=async()=>{if(!e.length){B("Không có user nào để scan. Vui lòng scan users trước.");return}C(!0);try{const D=e.map(X=>X.user_email);z("Bắt đầu scan storage cho tất cả users...");const I=await We("/api/storage/scan",{userEmails:D,forceRefresh:!0});z(`Scan hoàn thành: ${I.results.successful}/${I.results.totalUsers} thành công`),I.results.errors.length>0&&B(`${I.results.errors.length} users gặp lỗi khi scan`),await Mt()}catch(D){console.error("Error scanning storage:",D);const I=Me(D);T(`Lỗi scan storage: ${I.message}`,{showDetails:!0,details:I.details})}finally{C(!1)}},Q=async()=>{k(!0);try{z("Bắt đầu tính toán dung lượng local cho tất cả users...");const D=await We("/api/storage/calculate-local-all",{folderPath:w});z(`Tính toán hoàn thành: ${D.results.successful}/${D.results.totalUsers} thành công. Tổng: ${D.results.totalSizeFormatted}`),D.results.errors.length>0&&B(`${D.results.errors.length} users gặp lỗi khi tính toán`),await Mt()}catch(D){console.error("Error calculating all local sizes:",D);const I=Me(D);T(`Lỗi tính toán dung lượng local: ${I.message}`,{showDetails:!0,details:I.details})}finally{k(!1)}},R=async D=>{P(I=>new Set(I).add(D));try{const I=await We(`/api/storage/calculate-local/${D}`,{folderPath:w});z(`Đã tính toán dung lượng local cho ${D}: ${I.folderSizeFormatted}`),t(X=>X.map(oe=>oe.user_email===D?{...oe,local_downloaded_bytes:I.folderSize,local_folder_path:w}:oe))}catch(I){console.error("Error calculating local size:",I);const X=Me(I);T(`Lỗi tính toán dung lượng local: ${X.message}`,{showDetails:!0,details:X.details})}finally{P(I=>{const X=new Set(I);return X.delete(D),X})}},$=D=>{if(!D||D===0)return"0 B";const I=1024,X=["B","KB","MB","GB","TB"],oe=Math.floor(Math.log(D)/Math.log(I));return parseFloat((D/Math.pow(I,oe)).toFixed(2))+" "+X[oe]},O=D=>{const I=D.drive_usage_bytes||0,X=D.local_downloaded_bytes||0;return Math.max(0,I-X)},Y=D=>{const I=D.drive_usage_bytes||0,X=D.local_downloaded_bytes||0;return I===0?0:Math.min(100,X/I*100)},ne=D=>D>=90?"#4CAF50":D>=70?"#FF9800":"#F44336";return s.jsxs("div",{className:"storage-comparison",children:[s.jsxs("div",{className:"page-header",children:[s.jsx("h1",{children:"📊 So sánh dung lượng Storage"}),s.jsx("p",{children:"Đối chiếu dung lượng đã download vs dung lượng trên Google Drive"})]}),s.jsxs("div",{className:"controls-section",children:[s.jsxs("div",{className:"controls-row",children:[s.jsx("div",{className:"search-box",children:s.jsx("input",{type:"text",placeholder:"Tìm kiếm theo email...",value:u,onChange:D=>c(D.target.value),className:"search-input"})}),s.jsxs("div",{className:"sort-controls",children:[s.jsxs("select",{value:f,onChange:D=>v(D.target.value),className:"sort-select",children:[s.jsx("option",{value:"total_usage_bytes",children:"Tổng dung lượng"}),s.jsx("option",{value:"drive_usage_bytes",children:"Dung lượng Drive"}),s.jsx("option",{value:"local_downloaded_bytes",children:"Đã download"}),s.jsx("option",{value:"user_email",children:"Email"}),s.jsx("option",{value:"last_scanned_at",children:"Lần scan cuối"})]}),s.jsxs("select",{value:h,onChange:D=>S(D.target.value),className:"sort-select",children:[s.jsx("option",{value:"desc",children:"Giảm dần"}),s.jsx("option",{value:"asc",children:"Tăng dần"})]})]}),s.jsx("button",{onClick:pt,disabled:j||n,className:"btn btn-primary",children:j?"🔄 Đang scan...":"🔍 Scan Storage"}),s.jsx("button",{onClick:Q,disabled:y||n,className:"btn btn-secondary",children:y?"🔄 Đang tính toán...":"📊 Tính toán tất cả Local"})]}),s.jsx("div",{className:"controls-row",children:s.jsxs("div",{className:"folder-path-setting",children:[s.jsx("label",{children:"Đường dẫn thư mục local:"}),s.jsx("input",{type:"text",value:w,onChange:D=>x(D.target.value),placeholder:"E:\\",className:"folder-path-input"})]})})]}),i&&s.jsxs("div",{className:"overall-stats",children:[s.jsx("h3",{children:"📈 Thống kê tổng quan"}),s.jsxs("div",{className:"stats-grid",children:[s.jsxs("div",{className:"stat-item",children:[s.jsx("span",{className:"stat-label",children:"Tổng số users:"}),s.jsx("span",{className:"stat-value",children:i.totalUsers})]}),s.jsxs("div",{className:"stat-item",children:[s.jsx("span",{className:"stat-label",children:"Tổng dung lượng Drive:"}),s.jsx("span",{className:"stat-value",children:$(i.totalDriveUsed)})]}),s.jsxs("div",{className:"stat-item",children:[s.jsx("span",{className:"stat-label",children:"Tổng dung lượng Gmail:"}),s.jsx("span",{className:"stat-value",children:$(i.totalGmailUsed)})]}),s.jsxs("div",{className:"stat-item",children:[s.jsx("span",{className:"stat-label",children:"Users có lỗi:"}),s.jsx("span",{className:"stat-value error",children:i.usersWithErrors})]})]})]}),s.jsxs("div",{className:"users-table-container",children:[n&&s.jsx("div",{className:"loading",children:"🔄 Đang tải dữ liệu..."}),l&&s.jsx("div",{className:"error-message",children:"❌ Có lỗi xảy ra khi tải dữ liệu"}),!n&&!l&&e.length===0&&s.jsx("div",{className:"no-data",children:"📭 Không có dữ liệu storage. Vui lòng scan storage trước."}),!n&&!l&&e.length>0&&s.jsxs(s.Fragment,{children:[s.jsxs("table",{className:"users-table",children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"Email"}),s.jsx("th",{children:"Drive Usage"}),s.jsx("th",{children:"Gmail Usage"}),s.jsx("th",{children:"Total Usage"}),s.jsx("th",{children:"Downloaded"}),s.jsx("th",{children:"Missing"}),s.jsx("th",{children:"Progress"}),s.jsx("th",{children:"Actions"})]})}),s.jsx("tbody",{children:e.map(D=>{const I=O(D),X=Y(D),oe=E.has(D.user_email);return s.jsxs("tr",{children:[s.jsxs("td",{className:"email-cell",children:[s.jsxs("div",{className:"email-info",children:[s.jsx("span",{className:"email",children:D.user_email}),D.scan_error_message&&s.jsx("span",{className:"error-indicator",title:D.scan_error_message,children:"⚠️"})]}),D.last_scanned_at&&s.jsxs("div",{className:"last-scanned",children:["Scan: ",new Date(D.last_scanned_at).toLocaleString()]})]}),s.jsx("td",{children:$(D.drive_usage_bytes)}),s.jsx("td",{children:$(D.gmail_usage_bytes)}),s.jsx("td",{children:$(D.total_usage_bytes)}),s.jsxs("td",{className:"downloaded-cell",children:[s.jsx("span",{children:$(D.local_downloaded_bytes)}),D.local_folder_path&&s.jsxs("div",{className:"folder-path",children:["📁 ",D.local_folder_path,D.user_email]})]}),s.jsx("td",{className:I>0?"missing-bytes":"complete",children:$(I)}),s.jsx("td",{children:s.jsxs("div",{className:"progress-container",children:[s.jsx("div",{className:"progress-bar",style:{width:`${X}%`,backgroundColor:ne(X)}}),s.jsxs("span",{className:"progress-text",children:[X.toFixed(1),"%"]})]})}),s.jsx("td",{children:s.jsx("button",{onClick:()=>R(D.user_email),disabled:oe,className:"btn btn-small",title:"Tính toán dung lượng đã download",children:oe?"🔄":"📊"})})]},D.user_email)})})]}),d&&d.totalPages>1&&s.jsxs("div",{className:"pagination",children:[s.jsx("button",{onClick:()=>_(N-1),disabled:N<=1,className:"btn btn-small",children:"← Trước"}),s.jsxs("span",{className:"page-info",children:["Trang ",N," / ",d.totalPages,"(",d.totalCount," users)"]}),s.jsx("button",{onClick:()=>_(N+1),disabled:N>=d.totalPages,className:"btn btn-small",children:"Sau →"})]})]})]})]})}function bm(){return s.jsx($m,{children:s.jsx(pm,{children:s.jsxs("div",{className:"main-app",children:[s.jsx(Sm,{}),s.jsx("div",{className:"main-content",children:s.jsxs(Vp,{children:[s.jsx(Ot,{path:"/",element:s.jsx(Hp,{to:"/migration",replace:!0})}),s.jsx(Ot,{path:"/migration",element:s.jsx(Tm,{})}),s.jsx(Ot,{path:"/download",element:s.jsx(Om,{})}),s.jsx(Ot,{path:"/users",element:s.jsx(Hm,{})}),s.jsx(Ot,{path:"/user-management",element:s.jsx(Wm,{})}),s.jsx(Ot,{path:"/storage",element:s.jsx(Vm,{})})]})})]})})})}Ns.createRoot(document.getElementById("root")).render(s.jsx(Rd.StrictMode,{children:s.jsx(bm,{})}));
//# sourceMappingURL=index-Bv7q3C2U.js.map
